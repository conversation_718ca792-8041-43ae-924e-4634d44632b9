<template>
  <div class="map-container">
    <tech-panel v-show="currentType !== '对比分析'"></tech-panel>
    <div
      class="map-main"
      :class="currentType === '对比分析' ? 'width50' : ''"
      id="map3d"
      @mouseenter="leftMouseEnter"
      @mouseleave="leftMouseLeave"
    ></div>
    <div
      class="map-split"
      :class="currentType === '对比分析' ? 'width51' : ''"
    ></div>
    <div
      class="map-main"
      :class="currentType === '对比分析' ? 'width50' : 'hideTwoMap'"
      id="map3d2"
      @mouseenter="rightMouseEnter"
      @mouseleave="rightMouseLeave"
    ></div>

    <warn-panel
      v-show="
        currentType != '防洪预演' &&
        currentType != '对比分析' &&
        currentType != '避险转移'
      "
    ></warn-panel>
    <!-- <search-tool v-show="currentType != '对比分析'"></search-tool> -->
    <area-select
      v-show="currentType != '防洪预演' && currentType != '对比分析'"
    ></area-select>
    <cloud-play></cloud-play>
    <group-panel v-show="currentType != '对比分析'"></group-panel>
    <!-- <flood-pre-rehearsal v-if="currentType === '防洪预演' || currentType === '对比分析'"></flood-pre-rehearsal> -->
    <svg-panel v-if="currentType === '概化图'"></svg-panel>
    <el-button
      v-if="currentType === '对比分析'"
      type="primary"
      class="duibiBtn"
      @click="backOneMap"
      >退出对比</el-button
    >
  </div>
</template>

<script setup>
import * as turf from "@turf/turf";
import warnPanel from "./components/warnPanel";
import areaSelect from "./plugins/areaSelect";
import cloudPlay from "./plugins/cloudPlay";
import groupPanel from "./plugins/groupPanel";
import svgPanel from "./plugins/svgPanel";
import TechPanel from "./panels/TechPanel";
import img_yingxiang from "@/assets/icon/img.png";
import img_elec from "@/assets/icon/elec.png";
import img_ter from "@/assets/icon/ter.png";
import screenMark from "@/assets/images/screen-mark.png";
import { rainSumlist } from "@/api/watershed/screenRight/overview";
import { getRainTypeByDrp, getRiverColor } from "./utils/water-common";
import moment from "moment/moment";
import { selectStlyInfo, selectStlyList } from "@/api/watershed/ads";
import {
  villageList, // 村社列表
  queryEnterpriseList, // 企事业单位列表
  residentList, // 居民住宅列表
  queryRiskProblem, // 风险隐患列表
  placeList, // 安置点列表
  dangerousAreaList, // 危险区列表
  dangerousAreaDetail, // 危险区详情
  queryVillagePlacementList, // 村社安置点列表
  queryVillagePlacementDetail, // 村社安置点详情
} from "@/api/warning";
import {
  riverStationList,
  reservoirStationList,
} from "@/api/watershed/query/index";
import { getAliyunGeoJsonByCode } from "@/api/watershed/ads/index";
import { useMapPanelStore, useLayerControlStore } from "@/store/modules/map";
import { getWaterGateDetail } from "@/api/watershed/ads/index";
import { getRiverGeoList } from "@/api/watershed/ads/index";

let viewer = null;
let Cesium = null;
let labels = null; //集中管理地图要素的标注
let catchmentFillColor_viewer =
  DC.Color.fromCssColorString("rgba(83,179,231,0)");
let catchmentFillColor_viewer2 =
  DC.Color.fromCssColorString("rgba(83,179,231,0)");

const panelStore = useMapPanelStore();
const layerControlStore = useLayerControlStore();
const { changeVis } = layerControlStore;

const state = reactive({
  show: false,
  currentType: "态势分析", // 还有防洪预演 , 避险转移
  firstRainLoad: true, // 为了第一次过滤0的站点
  adcd: "", // 过滤行政区
  // 屏幕的监听事件,最好改成$Handler
  Handler: {},
  raindata: [],
  riverdata: [],
  reservoirdata: [],
  lycode: "",
  curStData: null, // 关键字搜索
});

const {
  show,
  currentType,
  firstRainLoad,
  adcd,
  Handler,
  raindata,
  riverdata,
  reservoirdata,
  lycode,
  curStData,
} = toRefs(state);

const formatTree = (data) => {
  let dataList = data.map((item) => {
    return {
      ...item.data,
      children: item.children && formatTree(item.children),
    };
  });

  return dataList;
};
// 添加雨量站
const addrainData = (data, rainlayer, rainHtmllayer, layerType = null) => {
  let position = new DC.Position(data.lgtd, data.lttd);
  let rainType = getRainTypeByDrp(data.todayRainfall);
  let Cesium = DC.getLib("Cesium");

  // 根据降雨量判断雨量站类型
  const isRaining = data.todayRainfall > 0;

  // 如果指定了图层类型，只添加到对应类型的图层
  if (layerType && layerType !== (isRaining ? "rain-yes" : "rain-no")) {
    return;
  }

  let point = new DC.PointPrimitive(position);
  point.setStyle({
    pixelSize: 12,
    color: Cesium.Color.fromCssColorString(rainType.color),
    outlineColor: Cesium.Color.fromCssColorString(rainType.color),
    outlineWidth: 0,
    scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 0.5),
    disableDepthTestDistance: Number.POSITIVE_INFINITY,
    distanceDisplayCondition: {
      near: 0, //最近距离
      far: 1000000, //最远距离
    },
  });
  point.on(DC.MouseEventType.CLICK, (e) => {
    panelStore.showMainPanel({
      STCD: e.overlay.attr.stcd,
      STNM: e.overlay.attr.stnm,
      STTP: "PP",
    });
  });
  point.on(DC.MouseEventType.MOUSE_OVER, (e) => {
    let data = e.overlay.attr;
    let pop1 = document.getElementById(data.stcd + rainlayer.id + "pop1");
    let pop2 = document.getElementById(data.stcd + rainlayer.id + "pop2");
    pop1.style.display = "none";
    pop2.style.display = "block";
    const parentElement = pop2.parentNode;
    const grandparentElement = parentElement.parentNode;
    grandparentElement.style["z-index"] = 1;
  });
  point.on(DC.MouseEventType.MOUSE_OUT, (e) => {
    let data = e.overlay.attr;
    let pop1 = document.getElementById(data.stcd + rainlayer.id + "pop1");
    let pop2 = document.getElementById(data.stcd + rainlayer.id + "pop2");
    pop1.style.display = "block";
    pop2.style.display = "none";
    const parentElement = pop2.parentNode;
    const grandparentElement = parentElement.parentNode;
    grandparentElement.style["z-index"] = "";
  });
  // 存储数据用于事件处理
  point.attr = data;
  // 添加到图层
  rainlayer.addOverlay(point);

  // 创建HTML标签显示雨量信息 - 只有当传入了HTML图层时才创建
  if (rainHtmllayer) {
    let divIcon = new DC.DivIcon(
      position,
      `<div ">
                  <div id="${
                    data.stcd + rainlayer.id + "pop1"
                  }" style="" class="marker-rain-html">${
        data.todayRainfall
      }mm</div>

                </div>`
    );

    let divIconPopDetail = new DC.DivIcon(
      position,
      `<div id="${
        data.stcd + rainlayer.id + "pop2"
      }" style="display: none" class="marker-rain-html2">
                    <div style="width: 100%;height: 30px;line-height: 30px"><div style="float: left;font-size: 24px;">${
                      data.todayRainfall
                    }</div><div style="float:left;line-height: 30px">mm</div></div>
                    <div style="width: 100%;height: 25px;font-size: 13px"><div style="float: left;max-width: 150px;text-align: left;" class="hideMoreText">${
                      data.stnm || "--"
                    }</div></div>
                    <hr class="popHr">
                    <div style="width: 100%;height: 25px;font-size: 14px;"><div style="float: left;max-width: 150px; margin-top: 5px; margin-bottom: 2.5px;" class="hideMoreText">${
                      data.adnm || "--"
                    }</div></div>
                    <div style="width: 100%;height: 25px;font-size: 12px;"><div  style="float: left;">${
                      data?.beforeTime + " 至 " + data?.nowTime
                    }</div></div>

                  </div>`
    );

    divIcon.attr = data;
    divIcon.setStyle({
      distanceDisplayCondition: {
        near: 0,
        far: 50000,
      },
    });
    divIconPopDetail.attr = data;
    divIconPopDetail.setStyle({
      distanceDisplayCondition: {
        near: 0,
        far: 500000,
      },
    });
    divIcon.on(DC.MouseEventType.CLICK, (e) => {
      panelStore.showMainPanel({
        STCD: e.overlay.attr.stcd,
        STNM: e.overlay.attr.stnm,
        STTP: "PP",
      });
    });
    rainHtmllayer.addOverlay(divIcon);
    rainHtmllayer.addOverlay(divIconPopDetail);
  }
};
const getRiverImg = (sttp, warnType, size) => {
  sttp = sttp || "ZQ";
  warnType = warnType || "";
  size = size || 32;
  let color = getRiverColor(warnType);
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");
  canvas.width = size;
  canvas.height = size;

  // 阴影设置
  ctx.save();
  ctx.shadowColor = "rgba(0, 0, 0, 0.35)";
  ctx.shadowBlur = 8;
  ctx.shadowOffsetX = 0;
  ctx.shadowOffsetY = 4;

  // 渐变填充
  let gradient = ctx.createLinearGradient(size * 0.5, 0, size * 0.5, size);
  // 亮色（顶部）
  gradient.addColorStop(0, lightenColor(color, 0.35));
  // 主色
  gradient.addColorStop(0.5, color);
  // 暗色（底部）
  gradient.addColorStop(1, darkenColor(color, 0.25));

  ctx.beginPath();
  ctx.moveTo(size * 0.25, 0);
  ctx.lineTo(size * 0.75, 0);
  ctx.lineTo(size * 0.5, size);
  ctx.closePath();
  if (sttp === "ZQ") {
    ctx.fillStyle = gradient;
    ctx.fill();
  } else {
    ctx.lineWidth = 3;
    ctx.strokeStyle = color;
    ctx.stroke();
  }
  ctx.restore();

  // 顶部高光线
  ctx.save();
  ctx.beginPath();
  ctx.moveTo(size * 0.28, size * 0.08);
  ctx.lineTo(size * 0.72, size * 0.08);
  ctx.lineWidth = 2.2;
  ctx.strokeStyle = "rgba(255,255,255,0.65)";
  ctx.shadowColor = "rgba(255,255,255,0.5)";
  ctx.shadowBlur = 2;
  ctx.stroke();
  ctx.restore();

  let image = new Image();
  image.src = canvas.toDataURL("image/png");
  return image;
};

// 辅助函数：亮化颜色
function lightenColor(color, percent) {
  // color: rgb/rgba/十六进制
  let c = parseToRgb(color);
  c.r = Math.min(255, c.r + (255 - c.r) * percent);
  c.g = Math.min(255, c.g + (255 - c.g) * percent);
  c.b = Math.min(255, c.b + (255 - c.b) * percent);
  return `rgb(${c.r},${c.g},${c.b})`;
}
// 辅助函数：加深颜色
function darkenColor(color, percent) {
  let c = parseToRgb(color);
  c.r = Math.max(0, c.r * (1 - percent));
  c.g = Math.max(0, c.g * (1 - percent));
  c.b = Math.max(0, c.b * (1 - percent));
  return `rgb(${c.r},${c.g},${c.b})`;
}
// 解析颜色字符串为rgb对象
function parseToRgb(color) {
  let ctx = document.createElement("canvas").getContext("2d");
  ctx.fillStyle = color;
  let computed = ctx.fillStyle;
  // 处理 #rrggbb
  if (computed[0] === "#") {
    let bigint = parseInt(computed.slice(1), 16);
    let r = (bigint >> 16) & 255;
    let g = (bigint >> 8) & 255;
    let b = bigint & 255;
    return { r, g, b };
  }
  // 处理 rgb/rgba
  let match = computed.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
  if (match) {
    return {
      r: parseInt(match[1]),
      g: parseInt(match[2]),
      b: parseInt(match[3]),
    };
  }
  // 默认黑色
  return { r: 0, g: 0, b: 0 };
}
// 添加河道站
const addriverData = (data, layer, htmllayer) => {
  let position = new DC.Position(data.lgtd, data.lttd);
  let image = getRiverImg(data.sttp, data.warnTypeVal, 32);

  let point = new DC.BillboardPrimitive(position, image);
  point.setStyle({
    // image: image,
    scale: 0.7,
    pixelOffset: new Cesium.Cartesian2(0, 0),
    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 0.5),
    disableDepthTestDistance: Number.POSITIVE_INFINITY,
    distanceDisplayCondition: {
      near: 0, //最近距离
      far: 1000000, //最远距离
    },
  });

  // 存储数据用于事件处理
  point.attr = data;

  // 添加点击事件
  point.on(DC.MouseEventType.CLICK, (e) => {
    panelStore.showMainPanel({
      STCD: e.overlay.attr.riverStationCode,
      STNM: e.overlay.attr.riverStationName,
      STTP: e.overlay.attr.sttp,
    });
  });
  point.on(DC.MouseEventType.MOUSE_OVER, (e) => {
    let data = e.overlay.attr;
    let pop1 = document.getElementById(
      data.riverStationCode + layer.id + "pop1"
    );
    let pop2 = document.getElementById(
      data.riverStationCode + layer.id + "pop2"
    );
    pop1.style.display = "none";
    pop2.style.display = "block";
    const parentElement = pop2.parentNode;
    const grandparentElement = parentElement.parentNode;
    grandparentElement.style["z-index"] = 1;
  });
  point.on(DC.MouseEventType.MOUSE_OUT, (e) => {
    let data = e.overlay.attr;
    let pop1 = document.getElementById(
      data.riverStationCode + layer.id + "pop1"
    );
    let pop2 = document.getElementById(
      data.riverStationCode + layer.id + "pop2"
    );
    pop1.style.display = "block";
    pop2.style.display = "none";
    const parentElement = pop2.parentNode;
    const grandparentElement = parentElement.parentNode;
    grandparentElement.style["z-index"] = "";
  });
  // 添加到图层
  layer.addOverlay(point);

  if (currentType.value !== "态势分析") return;

  // 根据预警状态区分
  let divIcon = null;
  let divIconPopDetail = null;
  let subClass = "";
  let subTitle = "";
  let subTitleColor = "";
  if (data?.wptn == "涨") {
    subClass = "up_marker";
    subTitle = "涨";
    subTitleColor = "#d7cd11";
  } else if (data?.wptn == "落") {
    subClass = "down_marker";
    subTitle = "落";
    subTitleColor = "#1eb738";
  } else if (data?.wptn == "平") {
    subClass = "ping_marker";
    subTitle = "平";
    subTitleColor = "#0cb7e3";
  }
  if (data.warnTypeVal) {
    // data.WARNTP 不同级别样式不一样可能
    data.warnTypeVal = data?.warnTypeVal.replace("水位", "");
    if (!data.warnTypeVal) return;

    let mainClass = "";
    let mainClassColor = "";
    if (data.warnTypeVal.indexOf("历史") > -1) {
      mainClass = "up-river";
      mainClassColor = "#FF7E01";
    } else if (data.warnTypeVal.indexOf("保证") > -1) {
      mainClass = "down-river";
      mainClassColor = "#FFA92F";
    } else if (data.warnTypeVal.indexOf("警戒") > -1) {
      mainClass = "ping-river";
      mainClassColor = "#FFEA35";
    }
    divIcon = new DC.DivIcon(
      position,
      `<div>
               <div id="${
                 data.riverStationCode + layer.id + "pop1"
               }" style="" class="marker-river-warn-html ${mainClass}">${
        data.warnTypeVal || "--"
      }m<div class="${subClass}" >${subTitle}</div></div>

             </div>`
    );
    divIconPopDetail = new DC.DivIcon(
      position,
      `
               <div id="${
                 data.riverStationCode + layer.id + "pop2"
               }" style="display: none" class="marker-river-warn-html2">
                 <div style="width: 100%;height: 30px;line-height: 30px"><div style="float: left;font-size: 24px;">${
                   data.waterLevel || "--"
                 }</div><div style="float:left;line-height: 30px">m</div><div style="float: right;color: ${subTitleColor}">${subTitle}</div></div>
                 <div style="width: 100%;height: 25px;font-size: 13px"><div style="float: left;max-width: 60px;text-align: left;" class="hideMoreText">${
                   data.riverStationName || "--"
                 }</div><div style="float:right;color: ${mainClassColor}" >${
        data.warnTypeVal || "--"
      }m</div></div>
                 <hr class="popHr">
                 <div style="width: 100%;height: 25px;font-size: 14px;"><div style="float: left;max-width: ${
                   data.sttp === "ZZ" ? 150 : 65
                 }px" class="hideMoreText">${
        data.adminRegionName || "--"
      }</div><div style="float: right;display: ${
        data.sttp === "ZZ" ? "none" : ""
      }" >流量${data.waterFlow || "--"}m³/s</div></div>
                 <div style="width: 100%;height: 25px;font-size: 14px;"><div  style="float: left;">${
                   data?.time || "--"
                 }</div></div>

               </div>
             `
    );
  } else {
    divIcon = new DC.DivIcon(
      position,
      `<div >
               <div id="${
                 data.riverStationCode + layer.id + "pop1"
               }" style="" class="marker-river-html">${
        data.waterLevel || "--"
      }m<div class="${subClass}" >${subTitle}</div></div>

             </div>`
    );
    divIconPopDetail = new DC.DivIcon(
      position,
      `
               <div id="${
                 data.riverStationCode + layer.id + "pop2"
               }" style="display: none" class="marker-river-warn-html2">
                 <div style="width: 100%;height: 30px;line-height: 30px"><div style="float: left;font-size: 24px;">${
                   data.waterLevel || "--"
                 }</div><div style="float:left;line-height: 30px">m</div><div style="float: right;color: ${subTitleColor}">${subTitle}</div></div>
                 <div style="width: 100%;height: 25px;font-size: 13px"><div style="float: left;max-width: 60px;text-align: left;" class="hideMoreText">${
                   data.riverStationName || "--"
                 }</div></div>
                 <hr class="popHr">
                 <div style="width: 100%;height: 25px;font-size: 14px;"><div style="float: left;max-width: ${
                   data.sttp === "ZZ" ? 150 : 65
                 }px" class="hideMoreText">${
        data.adminRegionName || "--"
      }</div><div style="float: right;display: ${
        data.sttp === "ZZ" ? "none" : ""
      }" >流量${data.waterFlow || "--"}m³/s</div></div>
                 <div style="width: 100%;height: 25px;font-size: 14px;"><div  style="float: left;">${
                   data?.time || "--"
                 }</div></div>

               </div>
               `
    );
  }

  divIcon.attr = data;
  divIcon.setStyle({
    distanceDisplayCondition: {
      near: 0,
      far: 10000,
    },
  });
  divIconPopDetail.attr = data;
  divIconPopDetail.setStyle({
    distanceDisplayCondition: {
      near: 0,
      far: 500000,
    },
  });
  divIcon.on(DC.MouseEventType.CLICK, (e) => {
    panelStore.showMainPanel({
      STCD: e.overlay.attr.riverStationCode,
      STNM: e.overlay.attr.riverStationName,
      STTP: e.overlay.attr.sttp,
    });
  });
  htmllayer.addOverlay(divIcon);
  htmllayer.addOverlay(divIconPopDetail);
};

// 添加水库站
const addreservoirData = (data, layer, htmllayer) => {
  let position = new DC.Position(data.lgtd, data.lttd);
  let point = new DC.BillboardPrimitive(
    position,
    "/icons/marker/reservoir.png"
  );
  point.setStyle({
    // image: "/icons/marker/reservoir.png",
    scale: 0.5,
    pixelOffset: new Cesium.Cartesian2(0, 0),
    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 0.5),
    disableDepthTestDistance: Number.POSITIVE_INFINITY,
    distanceDisplayCondition: {
      near: 0, //最近距离
      far: 1000000, //最远距离
    },
  });

  // 存储数据用于事件处理
  point.attr = data;

  // 添加点击事件
  point.on(DC.MouseEventType.CLICK, (e) => {
    panelStore.showMainPanel({
      STCD: e.overlay.attr.stcd,
      STNM: e.overlay.attr.stnm,
      STTP: "RR",
    });
  });
  point.on(DC.MouseEventType.MOUSE_OVER, (e) => {
    let data = e.overlay.attr;
    let pop1 = document.getElementById(data.stcd + layer.id + "pop1");
    let pop2 = document.getElementById(data.stcd + layer.id + "pop2");
    pop1.style.display = "none";
    pop2.style.display = "block";
    const parentElement = pop2.parentNode;
    const grandparentElement = parentElement.parentNode;
    grandparentElement.style["z-index"] = 1;
  });
  point.on(DC.MouseEventType.MOUSE_OUT, (e) => {
    let data = e.overlay.attr;
    let pop1 = document.getElementById(data.stcd + layer.id + "pop1");
    let pop2 = document.getElementById(data.stcd + layer.id + "pop2");
    pop1.style.display = "block";
    pop2.style.display = "none";
    const parentElement = pop2.parentNode;
    const grandparentElement = parentElement.parentNode;
    grandparentElement.style["z-index"] = "";
  });
  // 添加到图层
  layer.addOverlay(point);

  if (currentType.value !== "态势分析") return;
  let subClass = "";
  let subTitle = "";
  let subTitleColor = "";
  if (data?.wptn === "涨") {
    subClass = "up_marker";
    subTitle = "涨";
    subTitleColor = "#d7cd11";
  } else if (data?.wptn === "落") {
    subClass = "down_marker";
    subTitle = "落";
    subTitleColor = "#1eb738";
  } else if (data?.wptn === "平") {
    subClass = "ping_marker";
    subTitle = "平";
    subTitleColor = "#0cb7e3";
  }
  let mainClass = "";
  let mainClassColor = "";
  if (data?.warnTypeVal?.indexOf("历史") > -1) {
    mainClass = "up-river";
    mainClassColor = "#FF7E01";
  } else if (data?.warnTypeVal?.indexOf("保证") > -1) {
    mainClass = "down-river";
    mainClassColor = "#FFA92F";
  } else if (data?.warnTypeVal?.indexOf("警戒") > -1) {
    mainClass = "ping-river";
    mainClassColor = "#FFEA35";
  } else if (data?.warnTypeVal?.indexOf("汛限") > -1) {
    mainClass = "over-limit";
    mainClassColor = "#FFEB98";
  }
  // 根据预警状态区分
  let divIcon = null;

  divIcon = new DC.DivIcon(
    position,
    `<div ">
               <div id="${
                 data.stcd + layer.id + "pop1"
               }"  class="marker-reservoir-warn-html ${mainClass}"> ${
      data.waterLevel || "--"
    }m<div class="${subClass}" >${subTitle}</div></div>

             </div>`
  );
  divIcon.attr = data;
  divIcon.setStyle({
    distanceDisplayCondition: {
      near: 0,
      far: 10000,
    },
  });
  divIcon.on(DC.MouseEventType.CLICK, (e) => {
    panelStore.showMainPanel({
      STCD: e.overlay.attr.stcd,
      STNM: e.overlay.attr.stnm,
      STTP: "RR",
    });
  });
  htmllayer.addOverlay(divIcon);

  let divIconPopDetail = new DC.DivIcon(
    position,
    `<div id="${
      data.stcd + layer.id + "pop2"
    }" style="display: none" class="marker-reservoir-warn-html2">
                 <div style="width: 100%;height: 30px;line-height: 30px"><div style="float: left;font-size: 24px;">${
                   data.waterLevel || "--"
                 }</div><div style="float:left;line-height: 30px">m</div><div style="float: right;color: ${subTitleColor}">${subTitle}</div></div>
                 <div style="width: 100%;height: 25px;font-size: 13px"><div style="float: left;max-width: 130px;text-align: left;" class="hideMoreText">${
                   data.stnm || "--"
                 }</div></div>
                 <hr class="popHr">
                 <div style="width: 100%;height: 25px;font-size: 14px;"><div style="float: left;max-width: 65px" class="hideMoreText">${
                   data.adnm || "--"
                 }</div><div style="float: right">流量${
      data.inboundFlow || "--"
    }m³/s</div></div>
                 <div style="width: 100%;height: 25px;font-size: 14px;"><div  style="float: left;">${
                   data?.time || "--"
                 }</div></div>

               </div>`
  );
  divIconPopDetail.attr = data;
  divIconPopDetail.setStyle({
    distanceDisplayCondition: {
      near: 0,
      far: 500000,
    },
  });
  htmllayer.addOverlay(divIconPopDetail);
};

// 添加视频监控点位
const addVideoMonitoringData = (data, layer) => {
  try {
    let position = new DC.Position(data.longitude, data.latitude);

    // 创建视频监控点位
    let point = new DC.BillboardPrimitive(position, "/icons/marker/camera.png");

    point.setStyle({
      heightReference: 0,
      scale: 1,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      distanceDisplayCondition: {
        near: 0, //最近距离
        far: 500000, //最远距离
      },
    });
    point.size = [20, 20];
    point.attr = data;

    point.on(DC.MouseEventType.CLICK, (e) => {
      panelStore.showMainPanel({
        STCD: e.overlay.attr.indexCode,
        STNM: e.overlay.attr.name,
        STTP: "VIDEO",
        data: e.overlay.attr,
      });
    });

    layer.addOverlay(point);
    layer.show = true;
  } catch (error) {
    console.error("添加视频监控点位失败:", error, data);
  }
};

// start 防洪 预演的水位河道
// 添加河道站
const addriver3Data = (data, layer, htmllayer) => {
  let position = new DC.Position(data.lgtd, data.lttd);
  let image = getRiverImg(data.sttp, data.warnTypeVal, 32);

  // 创建点位
  let primitiveLayer = new DC.PrimitiveLayer(
    "river3-primitive-" + data.riverStationCode
  );
  layer._viewer.addLayer(primitiveLayer);

  let point = new DC.BillboardPrimitive(position);
  point.setStyle({
    image: image,
    scale: 0.6,
    pixelOffset: new Cesium.Cartesian2(0, 0),
    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    disableDepthTestDistance: Number.POSITIVE_INFINITY,
    scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 0.5),
  });

  // 存储数据用于事件处理
  point.attr = data;

  // 添加点击事件
  point.on(DC.MouseEventType.CLICK, (e) => {
    if (currentType.value === "对比分析") {
      panelStore.showComparisonPanel({
        STCD: e.overlay.attr.riverStationCode,
        STNM: e.overlay.attr.riverStationName,
        STTP: e.overlay.attr.sttp,
      });
    } else {
      panelStore.showMainPanel({
        STCD: e.overlay.attr.riverStationCode,
        STNM: e.overlay.attr.riverStationName,
        STTP: e.overlay.attr.sttp,
      });
    }
  });

  // 添加到图层
  primitiveLayer.addOverlay(point);

  // 根据预警状态区分
  let divIcon = new DC.DivIcon(
    position,
    `<div onmouseover="showMorePopInfo2('${
      data.riverStationCode + layer.id
    }')"  onmouseout="hideMorePopInfo2('${data.riverStationCode + layer.id}')">
             <div id="${
               data.riverStationCode + layer.id + "pop2"
             }" class="marker-river-warn-html2">
               <div style="width: 100%;height: 25px;font-size: 13px"><div style="float: left;text-align: center;;width: 100%;" class="hideMoreText">${
                 data.riverStationName || "--"
               }</div></div>
               <hr class="popHr">
               <div style="width: 100%;height: 25px;line-height: 25px"><div style="float: left;font-size: 14px;">最高水位: ${
                 data.waterLevel || "--"
               }</div><div style="float:left;line-height: 30px">m</div></div>
               <div style="width: 100%;height: 25px;font-size: 14px;line-height: 25px"><div style="float: left;">最大流量: ${
                 data.waterFlow || "--"
               }m³/s</div></div>
               <div style="width: 100%;height: 25px;font-size: 14px;line-height: 25px"><div  style="float: left;">出现时间: ${
                 data?.time || "--"
               }</div></div>
               <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
             </div>
           </div>`
  );
  divIcon.attr = data;
  divIcon.setStyle({
    distanceDisplayCondition: {
      near: 0,
      far: 1000000,
    },
  });
  divIcon.on(DC.MouseEventType.CLICK, (e) => {
    if (currentType.value === "对比分析") {
      panelStore.showComparisonPanel({
        STCD: e.overlay.attr.riverStationCode,
        STNM: e.overlay.attr.riverStationName,
        STTP: e.overlay.attr.sttp,
      });
    } else {
      panelStore.showMainPanel({
        STCD: e.overlay.attr.riverStationCode,
        STNM: e.overlay.attr.riverStationName,
        STTP: e.overlay.attr.sttp,
      });
    }
  });
  if (currentType.value === "对比分析") {
  } else {
    htmllayer.addOverlay(divIcon);
  }
};
// 添加水库站
const addreservoir3Data = (data, layer, htmllayer) => {
  let position = new DC.Position(data.LGTD, data.LTTD);

  // 创建点位
  let primitiveLayer = new DC.PrimitiveLayer(
    "reservoir3-primitive-" + data.STCD
  );
  layer._viewer.addLayer(primitiveLayer);

  let point = new DC.BillboardPrimitive(position);
  point.setStyle({
    image: "/icons/marker/reservoir.png",
    scale: 0.5,
    pixelOffset: new Cesium.Cartesian2(0, 0),
    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    disableDepthTestDistance: Number.POSITIVE_INFINITY,
    scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 0.5),
  });

  // 存储数据用于事件处理
  point.attr = data;

  // 添加点击事件
  point.on(DC.MouseEventType.CLICK, (e) => {
    if (currentType.value === "对比分析") {
      panelStore.showComparisonPanel({
        STCD: e.overlay.attr.STCD,
        STNM: e.overlay.attr.STNM,
        STTP: "RR",
      });
    } else {
      panelStore.showMainPanel({
        STCD: e.overlay.attr.STCD,
        STNM: e.overlay.attr.STNM,
        STTP: "RR",
      });
    }
  });

  // 添加到图层
  primitiveLayer.addOverlay(point);

  // 根据预警状态区分
  let divIcon = new DC.DivIcon(
    position,
    `<div onmouseover="showMorePopInfo2('${
      data.STCD + layer.id
    }')"  onmouseout="hideMorePopInfo2('${data.STCD + layer.id}')">
               <div id="${
                 data.STCD + layer.id + "pop2"
               }"  class="marker-reservoir-warn-html2">
                 <div style="width: 100%;height: 22px;font-size: 13px"><div style="float: left;text-align: center;width: 100%;" class="hideMoreText">${
                   data.STNM || "--"
                 }</div></div>
                 <hr class="popHr">
                 <div style="width: 100%;height: 22px;font-size: 14px;"><div style="float: left;">入库流量: ${
                   data.INQ || "--"
                 }m³/s</div></div>
                 <div style="width: 100%;height: 22px;font-size: 14px;"><div style="float: left;">出库流量: ${
                   data.OTQ || "--"
                 }m³/s</div></div>
                 <div style="width: 100%;height: 22px;font-size: 14px;"><div style="float: left">库水位: ${
                   data.Z || "--"
                 }m</div></div>
                 <div style="width: 100%;height: 22px;font-size: 14px;"><div  style="float: left;">出现时间: ${
                   data?.YMDH.substring(5, 16).replace("T", " ") || "--"
                 }</div></div>
                 <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
               </div>
             </div>`
  );
  divIcon.attr = data;
  divIcon.setStyle({
    distanceDisplayCondition: {
      near: 0,
      far: 1000000,
    },
  });
  divIcon.on(DC.MouseEventType.CLICK, (e) => {
    if (currentType.value === "对比分析") {
      panelStore.showComparisonPanel({
        STCD: e.overlay.attr.STCD,
        STNM: e.overlay.attr.STNM,
        STTP: "RR",
      });
    } else {
      panelStore.showMainPanel({
        STCD: e.overlay.attr.STCD,
        STNM: e.overlay.attr.STNM,
        STTP: "RR",
      });
    }
  });
  if (currentType.value === "对比分析") {
  } else {
    htmllayer.addOverlay(divIcon);
  }
};
// end

// 添加村社点位
const addTownshipData = (data, layer, htmllayer) => {
  let position = new DC.Position(data.lgtd, data.lttd);
  let point = new DC.BillboardPrimitive(
    position,
    "/icons/marker/house-poi.png"
  );
  point.setStyle({
    heightReference: 0,
    scale: 1,
    disableDepthTestDistance: Number.POSITIVE_INFINITY,
    distanceDisplayCondition: {
      near: 0, //最近距离
      far: 50000, //最远距离
    },
    // pixelOffset: new Cesium.Cartesian2(0, 0),
    // verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    // scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 1)
  });
  point.size = [20, 20];
  // 存储数据用于事件处理
  point.attr = data;

  // 添加到图层
  layer.addOverlay(point);

  if (currentType.value !== "态势分析") return;
  // 创建HTML标签显示信息
  let divIcon = new DC.DivIcon(
    position,
    `<div onmouseover="showMorePopInfo('${
      data.adcd + layer.id
    }')"  onmouseout="hideMorePopInfo('${data.adcd + layer.id}')">
       <div id="${
         data.adcd + layer.id + "pop1"
       }" class="marker-township-html">${data.adnm || "--"}</div>
       <div id="${
         data.adcd + layer.id + "pop2"
       }" style="display: none" class="marker-township-html2">
         <div style="width: 100%;height: 30px;line-height: 30px"><div style="float: left;font-size: 16px;">${
           data.adnm || "--"
         }</div></div>

         <hr class="popHr">
         <div style="width: 100%;height: 25px;font-size: 14px;"><div style="float: left;max-width: 150px" class="hideMoreText">户籍人口：${
           data.registeredPopulation || "--"
         }</div></div>
         <div style="width: 100%;height: 25px;font-size: 12px;"><div style="float: left;">常住人口：${
           data.contacts || "--"
         }</div></div>
         <div style="width: 100%;height: 25px;font-size: 12px;"><div style="float: left;">防洪能力：${
           data.floodControlCapacity || "--"
         }</div></div>
         <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
       </div>
     </div>`
  );

  divIcon.attr = data;
  divIcon.setStyle({
    distanceDisplayCondition: {
      near: 0,
      far: 10000,
    },
  });
  htmllayer.addOverlay(divIcon);
  // 隐藏图层
  layer.show = false;
  htmllayer.show = false;
};

// 添加企事业单位点位
const addEnterpriseData = (data, layer, htmllayer) => {
  let position = new DC.Position(data.longitude, data.latitude);
  let point = new DC.BillboardPrimitive(
    position,
    "/icons/marker/enterprises-poi.png"
  );
  point.setStyle({
    heightReference: 0,
    scale: 1,
    disableDepthTestDistance: Number.POSITIVE_INFINITY,
    distanceDisplayCondition: {
      near: 0, //最近距离
      far: 50000, //最远距离
    },
    // pixelOffset: new Cesium.Cartesian2(0, 0),
    // verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    // scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 1)
  });
  point.size = [20, 20];
  point.attr = data;
  layer.addOverlay(point);

  let divIcon = new DC.DivIcon(
    position,
    `<div onmouseover="showMorePopInfo('${
      data.id + layer.id
    }')"  onmouseout="hideMorePopInfo('${data.id + layer.id}')">
          <div id="${
            data.id + layer.id + "pop1"
          }" class="marker-enterprise-html">${data.name || "--"}</div>
          <div id="${
            data.id + layer.id + "pop2"
          }" style="display: none" class="marker-enterprise-html2">
            <div class="marker-title">${data.name || "--"}</div>
            <div class="marker-content">

              <hr class="popHr">
              <div class="marker-item">
                <span class="marker-label">房屋数量：</span>
                <span class="marker-value">${data.houseCount || "--"}</span>
              </div>
              <div class="marker-item">
                <span class="marker-label">在岗人数：</span>
                <span class="marker-value">${data.staffCount || "--"}人</span>
              </div>
              <div class="marker-item">
                <span class="marker-label">占地面积：</span>
                <span class="marker-value">${data.area || "--"}Km²</span>
              </div>
            </div>
            <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
          </div>
        </div>`
  );
  divIcon.attr = data;
  divIcon.setStyle({
    distanceDisplayCondition: {
      near: 0,
      far: 10000,
    },
  });
  htmllayer.addOverlay(divIcon);

  // 隐藏图层
  layer.show = false;
  htmllayer.show = false;
};

// 添加居民住宅点位
const addResidentData = (data, layer, htmllayer) => {
  let position = new DC.Position(data.lgtd, data.lttd);
  let point = new DC.BillboardPrimitive(
    position,
    "/icons/marker/household-poi.png"
  );
  point.setStyle({
    heightReference: 0,
    scale: 1,
    disableDepthTestDistance: Number.POSITIVE_INFINITY,
    distanceDisplayCondition: {
      near: 0, //最近距离
      far: 50000, //最远距离
    },
    // pixelOffset: new Cesium.Cartesian2(0, 0),
    // verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    // scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 1)
  });
  point.size = [20, 20];
  point.attr = data;
  layer.addOverlay(point);
  let divIcon = new DC.DivIcon(
    position,
    `<div onmouseover="showMorePopInfo('${
      data.id + layer.id
    }')"  onmouseout="hideMorePopInfo('${data.id + layer.id}')">
          <div id="${
            data.id + layer.id + "pop1"
          }" class="marker-resident-html" style="display: none">${
      data.name || "--"
    }</div>
          <div id="${
            data.id + layer.id + "pop2"
          }" style="display: none" class="marker-resident-html2">
            <div class="marker-title">${data.name || "--"}</div>
            <div class="marker-content">

              <hr class="popHr">
              <div class="marker-item">
                <span class="marker-label">户数：</span>
                <span class="marker-value">${data.households || "--"}户</span>
              </div>
              <div class="marker-item">
                <span class="marker-label">常住人口：</span>
                <span class="marker-value">${
                  data.householdSize || "--"
                }人</span>
              </div>
              <div class="marker-item">
                <span class="marker-label">防洪能力：</span>
                <span class="marker-value">${"--"}</span>
              </div>
            </div>
            <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
          </div>
        </div>`
  );
  divIcon.attr = data;
  divIcon.setStyle({
    distanceDisplayCondition: {
      near: 0,
      far: 5000,
    },
  });
  htmllayer.addOverlay(divIcon);

  // 隐藏图层
  layer.show = false;
  htmllayer.show = false;
};

// 添加风险隐患点位
const addRiskProblemData = (data, layer, htmllayer) => {
  let position = new DC.Position(data.longitude, data.latitude);
  let point = new DC.BillboardPrimitive(
    position,
    "/icons/marker/riskproblem-poi.png"
  );
  point.setStyle({
    heightReference: 0,
    scale: 1,
    disableDepthTestDistance: Number.POSITIVE_INFINITY,
    distanceDisplayCondition: {
      near: 0, //最近距离
      far: 50000, //最远距离
    },
    // pixelOffset: new Cesium.Cartesian2(0, 0),
    // verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    // scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 1)
  });
  point.size = [20, 20];
  point.attr = data;
  layer.addOverlay(point);

  if (currentType.value !== "态势分析") return;

  let divIcon = new DC.DivIcon(
    position,
    `<div onmouseover="showMorePopInfo('${
      data.id + layer.id
    }')"  onmouseout="hideMorePopInfo('${data.id + layer.id}')">
          <div id="${data.id + layer.id + "pop1"}" class="marker-risk-html">${
      data.name || "--"
    }</div>
          <div id="${
            data.id + layer.id + "pop2"
          }" style="display: none" class="marker-risk-html2">
            <div class="marker-title">${data.name || "--"}</div>
            <div class="marker-content">

              <hr class="popHr">
              <div class="marker-item">
                <span class="marker-label">危险因素：</span>
                <span class="marker-value">${
                  getDangerTypeText(data.type) || "--"
                }</span>
              </div>
              <div class="marker-item">
                <span class="marker-label">影响村社：</span>
                <span class="marker-value">${data.villageNames || "--"}</span>
              </div>
            </div>
            <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
          </div>
        </div>`
  );
  divIcon.attr = data;
  divIcon.setStyle({
    distanceDisplayCondition: {
      near: 0,
      far: 10000,
    },
  });
  htmllayer.addOverlay(divIcon);

  // 隐藏图层
  layer.show = false;
  htmllayer.show = false;
};

// 添加安置点点位
const addPlaceData = (data, layer, htmllayer) => {
  let position = new DC.Position(data.lgtd, data.lttd);
  let point = new DC.BillboardPrimitive(
    position,
    "/icons/marker/place-poi.png"
  );
  point.setStyle({
    heightReference: 0,
    scale: 1,
    disableDepthTestDistance: Number.POSITIVE_INFINITY,
    distanceDisplayCondition: {
      near: 0, //最近距离
      far: 50000, //最远距离
    },
    // pixelOffset: new Cesium.Cartesian2(0, 0),
    // verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    // scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 1)
  });
  point.size = [20, 20];
  point.attr = data;
  layer.addOverlay(point);

  //  console.log(data)

  let divIcon = new DC.DivIcon(
    position,
    `<div onmouseover="showMorePopInfo('${
      data.id + layer.id
    }')"  onmouseout="hideMorePopInfo('${data.id + layer.id}')">
          <div id="${data.id + layer.id + "pop1"}" class="marker-place-html">${
      data.name || "--"
    }</div>
          <div id="${
            data.id + layer.id + "pop2"
          }" style="display: none" class="marker-place-html2">
            <div class="marker-title">${data.name || "--"}</div>
            <div class="marker-content">

              <hr class="popHr">
              <div class="marker-item">
                <span class="marker-label">容纳人数：</span>
                <span class="marker-value">${
                  data.resettlementPeoNum || "--"
                }人</span>
              </div>
              <div class="marker-item">
                <span class="marker-label">负责人：</span>
                <span class="marker-value">${data.notes || "--"}</span>
              </div>
              <div class="marker-item">
                <span class="marker-label">联系方式：</span>
                <span class="marker-value">${data.phone || "--"}</span>
              </div>
              <div class="marker-item">
                <span class="marker-label">地址:</span>
                <span class="marker-value">${data.address || "--"}</span>
              </div>
            </div>
            <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
          </div>
        </div>`
  );
  divIcon.attr = data;
  divIcon.setStyle({
    distanceDisplayCondition: {
      near: 0,
      far: 10000,
    },
  });
  htmllayer.addOverlay(divIcon);

  // 隐藏图层
  layer.show = false;
  htmllayer.show = false;
};
// 添加危险区面数据
const addDangerAreaData = (data, layer, htmllayer, hazardouName) => {
  if (
    !data ||
    !data.features ||
    !data.features[0] ||
    !data.features[0].geometry
  ) {
    console.warn("Invalid GeoJSON data for danger area");
    return;
  }
  try {
    // 获取多边形坐标
    const coordinates = data.features[0].geometry.coordinates[0][0];
    const maskpointArray = [];

    // 转换坐标格式
    for (let i = 0, l = coordinates.length; i < l; i++) {
      maskpointArray.push(coordinates[i][0]);
      maskpointArray.push(coordinates[i][1]);
    }

    // 创建面图层
    const polygon = new DC.Polygon(
      Cesium.Cartesian3.fromDegreesArray(maskpointArray)
    );

    // 设置面的样式
    polygon.setStyle({
      material: DC.Color.fromCssColorString("rgba(255, 0, 0, 0.3)"), // 半透明红色填充
      outline: true,
      outlineColor: DC.Color.fromCssColorString("rgba(255, 0, 0, 1)"), // 红色边框
      outlineWidth: 2,
      classificationType: 2, // 同时影响地形和3D切片
      distanceDisplayCondition: {
        near: 0,
        far: 50000,
      },
    });

    // 添加到图层
    layer.addOverlay(polygon);

    // 获取多边形中心点用于标注
    const center = turf.centerOfMass(data);
    if (center && center.geometry) {
      const centerPos = new DC.Position(
        center.geometry.coordinates[0],
        center.geometry.coordinates[1]
      );

      // 添加HTML标注
      const divIcon = new DC.DivIcon(
        centerPos,
        `<div class="danger-area-container">
              <div class="danger-area-label">${hazardouName || "危险区"}</div>
              <div class="danger-area-popup">
                <div class="marker-title">${hazardouName || "危险区"}</div>
                <div class="marker-content">
                  <hr class="popHr">
                  <div class="marker-item">
                    <span class="marker-label">危险区等级：</span>
                    <span class="marker-value">${
                      data.features[0].properties?.level === 1
                        ? "极高风险"
                        : data.features[0].properties?.level === 2
                        ? "高风险"
                        : "低风险"
                    }</span>
                  </div>
                  <div class="marker-item">
                    <span class="marker-label">汇流成灾时间：</span>
                    <span class="marker-value">${
                      data.features[0].properties?.disasterConvergenceTime ||
                      "--"
                    }小时</span>
                  </div>
                </div>
                <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
              </div>
            </div>`
      );

      divIcon.setStyle({
        distanceDisplayCondition: {
          near: 0,
          far: 10000,
        },
      });

      htmllayer.addOverlay(divIcon);
      // 隐藏图层
      layer.show = false;
      htmllayer.show = false;
    }
  } catch (error) {
    console.error("Failed to add danger area:", error);
  }
};
//添加村社转移安置点转移路线
const addRoad = (data, layer, htmllayer, routeId) => {
  if (
    !data ||
    !data.features ||
    !data.features[0] ||
    !data.features[0].geometry
  ) {
    console.warn("Invalid GeoJSON data for road");
    return;
  }
  try {
    let geom = null;
    geom = data.features[0].geometry.coordinates[0];

    // 转换坐标点为 Cesium 坐标数组
    const positions = geom.map((coord) =>
      Cesium.Cartesian3.fromDegrees(coord[0], coord[1])
    );

    // Define the GLSL shader source
    const flowingArrowShaderSource = `
          uniform vec4 color;
          uniform sampler2D image;
          uniform float speed;
          uniform float time;
          uniform float brightness;
          uniform float repeat; // Ensure repeat uniform is declared

          czm_material czm_getMaterial(czm_materialInput materialInput) {
              czm_material material = czm_getDefaultMaterial(materialInput);
              vec2 st = materialInput.st;
              // Scale the s-coordinate by repeat value
              float scaled_s = st.s * repeat;
              // Calculate flow offset using the scaled coordinate
              float flowOffset = fract(scaled_s - time * speed);
              // Use the calculated flow offset for texture lookup
              vec2 flowingCoord = vec2(flowOffset, st.t);
              // 获取箭头纹理 - 使用 texture 替代 texture2D
              vec4 arrowColor = texture(image, flowingCoord);
              float glow = sin(time * 8.0) * 0.3 + 0.7;
              float edge = smoothstep(0.1, 0.6, st.t) * smoothstep(0.9, 0.4, st.t);
              vec3 baseColor = color.rgb * brightness;
              vec3 glowColor = baseColor + vec3(0.2, 0.5, 0.8) * glow;
              vec3 finalColor = mix(baseColor, glowColor, edge) * (0.8 + glow * 0.2);
              // Ensure alpha considers texture and edge effects correctly
              float alpha = arrowColor.a * color.a * (0.6 + edge * 0.4);
              // Make sure only the arrow part is visible, not the transparent background if the texture has one
              // Assuming the arrow texture's non-transparent part has alpha > 0.1
              alpha = alpha * step(0.1, arrowColor.a);

              material.diffuse = finalColor;
              material.alpha = alpha;
              material.emission = finalColor * glow * 0.3;
              return material;
          }
        `;

    // // Define the material using standard Cesium API
    const flowingMaterial = new Cesium.Material({
      fabric: {
        type: "FlowingArrowPrimitive", // Unique type name for primitive
        uniforms: {
          color: Cesium.Color.fromCssColorString("#00a8ff").withAlpha(1),
          image: "/icons/marker/jt.png",
          speed: 0.5,
          time: 0, // Initial time
          brightness: 1.2,
          repeat: 30.0, // Ensure this value results in desired repetition density
        },
        source: flowingArrowShaderSource,
      },
      translucent: () => true,
    });

    // // Create the appearance 外观
    const appearance = new Cesium.PolylineMaterialAppearance({
      material: flowingMaterial,
    });

    // // Create the polyline geometry instance
    const polylineInstance = new Cesium.GeometryInstance({
      geometry: new Cesium.PolylineGeometry({
        positions: positions,
        width: 25, // Line width
        vertexFormat: Cesium.PolylineMaterialAppearance.VERTEX_FORMAT, // Required for material appearance
      }),
      id: "road-primitive-" + routeId,
    });
    // Create the primitive and add it to the scene
    const roadPrimitive = window.viewer.scene.primitives.add(
      new Cesium.Primitive({
        geometryInstances: polylineInstance,
        appearance: appearance,
        asynchronous: false, // Load synchronously for simplicity here
        id: "road-primitive-" + routeId,
      })
    );
    roadPrimitive.show = false;

    // Update the time uniform in the preRender event
    const preRenderListener = function (scene, time) {
      // Check if the primitive and material still exist
      if (
        roadPrimitive &&
        !roadPrimitive.isDestroyed &&
        appearance &&
        appearance.material &&
        appearance.material.uniforms
      ) {
        appearance.material.uniforms.time = performance.now() / 1000.0;
      } else {
        // Clean up listener if primitive is gone
        window.viewer.scene.preRender.removeEventListener(preRenderListener);
      }
    };
    window.viewer.scene.preRender.addEventListener(preRenderListener);
  } catch (error) {
    console.error("Failed to add road:", error);
  }
};
//添加水闸点位 真实接口读取数据
const addWaterGateData = (data, layer, htmllayer) => {
  let position = new DC.Position(data.lgtd, data.lttd);
  let point = new DC.BillboardPrimitive(position, "/icons/marker/zhamen.png");
  point.setStyle({
    heightReference: 0,
    scale: 1,
    disableDepthTestDistance: Number.POSITIVE_INFINITY,
    distanceDisplayCondition: {
      near: 0, //最近距离
      far: 500000, //最远距离
    },
  });
  point.size = [20, 20];
  point.attr = data;

  point.on(DC.MouseEventType.CLICK, (e) => {
    panelStore.showMainPanel({
      STCD: 2,
      STNM: `${data.name}`,
      STTP: "Gate",
      data: data,
    });
  });
  //鼠标移入移出事件
  point.on(DC.MouseEventType.MOUSE_OVER, (e) => {
    let data = e.overlay.attr;
    let pop1 = document.getElementById(data.name + layer.id + "pop1");
    let pop2 = document.getElementById(data.name + layer.id + "pop2");
    pop1.style.display = "none";
    pop2.style.display = "block";
    const parentElement = pop2.parentNode;
    const grandparentElement = parentElement.parentNode;
    grandparentElement.style["z-index"] = 1;
  });
  point.on(DC.MouseEventType.MOUSE_OUT, (e) => {
    let data = e.overlay.attr;
    let pop1 = document.getElementById(data.name + layer.id + "pop1");
    let pop2 = document.getElementById(data.name + layer.id + "pop2");
    pop1.style.display = "block";
    pop2.style.display = "none";
    const parentElement = pop2.parentNode;
    const grandparentElement = parentElement.parentNode;
    grandparentElement.style["z-index"] = "";
  });
  layer.addOverlay(point);
  layer.show = true;
  // ===== 新增：接口接入数据字段 =====
  data.flow = data.dischargeFlow;
  data.upLevel = data.upstreamLevel;
  data.downLevel = data.downstreamLevel;
  data.time = data.recordTime;

  // ===== 新增：主标签和详情弹窗 =====
  let divIcon = new DC.DivIcon(
    position,
    `
        <div>
        <div id="${
          data.name + layer.id + "pop1"
        }" class="marker-watergate-html">${data.name || "--"}</div>
        </div>
        `
  );
  let divIconPopDetail = new DC.DivIcon(
    position,
    `<div id="${
      data.name + layer.id + "pop2"
    }" style="display: none" class="marker-watergate-html2">
          <div class="watergate-flow">${data.flow || "-- "}m³/s</div>
          <div class="watergate-name">${data.name || "-- "}</div>
          <hr class="popHr">
          <div class="watergate-levels">
            <span class="watergate-label">闸上</span><span class="watergate-value">${
              data.upLevel || "-- "
            }m</span>
            <span class="watergate-label">闸下</span><span class="watergate-value">${
              data.downLevel || "-- "
            }m</span>
          </div>
          <div class="watergate-time">${data.time || "-- "}</div>

        </div>`
  );
  divIcon.attr = data;
  divIcon.setStyle({
    distanceDisplayCondition: {
      near: 0,
      far: 50000,
    },
  });
  divIcon.on(DC.MouseEventType.CLICK, (e) => {
    panelStore.showMainPanel({
      STCD: `${data.stcd}`,
      STNM: `${data.name}`,
      STTP: "Gate",
    });
  });
  divIconPopDetail.attr = data;
  divIconPopDetail.setStyle({
    distanceDisplayCondition: {
      near: 0,
      far: 500000,
    },
  });
  divIconPopDetail.on(DC.MouseEventType.CLICK, (e) => {
    panelStore.showMainPanel({
      STCD: `${data.stcd}`,
      STNM: `${data.name}`,
      STTP: "Gate",
    });
  });
  // 添加到htmllayer
  if (htmllayer) {
    htmllayer.addOverlay(divIcon);
    htmllayer.addOverlay(divIconPopDetail);
  }
};
const addDataFunc = {
  addrainData,
  addriverData,
  addreservoirData,
  addriver3Data,
  addreservoir3Data,
  addTownshipData,
  addEnterpriseData,
  addResidentData,
  addRiskProblemData,
  addPlaceData,
  // addDangerAreaData,
  // addRoad,
};
const getLayerData = (viewer) => {
  // 获取雨情列表
  getRainList(viewer);
  // 获取河道水情列表
  getRiverList(viewer);
  // 获取水库水情列表 - 先隐藏
  getRsvrList(viewer);

  // 获取村社列表
  getTownshipList(viewer);
  // // 获取安置点列表
  getPlaceList(viewer);
  // // 获取企事业单位列表
  getEnterpriseList(viewer);
  // // 获取居民住宅列表
  getResidentList(viewer);
  // // 获取风险隐患列表
  getRiskProblemList(viewer);
  // 获取危险区列表
  getDangerAreaList(viewer);
  // 获取转移路线列表
  getRoadList(viewer);
  // 获取视频监控列表
  getVideoMonitoringList(viewer);
  // 获取水闸列表
  getWaterGateList(viewer);
};

// 获取洪水淹没的数据
const getFloodData = () => {
  let layer = new DC.GeoJsonLayer(
    "layer",
    "./datas/flood/1731520716025032705.json"
  );
  viewer.addLayer(layer);
  viewer.flyTo(layer);
};
//雨情列表
const getRainList = async (viewer, hour) => {
  let obj2;
  let hourNum = hour || 24;
  const res2 = await rainSumlist(hourNum);
  let nowTime = moment().format("MM-DD HH");
  let beforeTime = moment().subtract(hourNum, "hours").format("MM-DD HH");
  clearLayer("rain", viewer);
  obj2 = res2.data;

  // 创建有雨雨量站图层
  let rainYesLayer = viewer.getLayer("rain-yes-primitive");
  if (rainYesLayer) {
    rainYesLayer.clear();
  } else {
    rainYesLayer = new DC.PrimitiveLayer("rain-yes-primitive");
    window.viewer.addLayer(rainYesLayer);
  }

  // 创建无雨雨量站图层（默认隐藏）
  let rainNoLayer = viewer.getLayer("rain-no-primitive");
  if (rainNoLayer) {
    rainNoLayer.clear();
  } else {
    rainNoLayer = new DC.PrimitiveLayer("rain-no-primitive");
    rainNoLayer.show = false; // 默认隐藏无雨雨量站
    window.viewer.addLayer(rainNoLayer);
  }

  // 保持原有的rain-primitive图层用于兼容性
  let rainlayer = viewer.getLayer("rain-primitive");
  if (rainlayer) {
    rainlayer.clear();
  } else {
    rainlayer = new DC.PrimitiveLayer("rain-primitive");
    window.viewer.addLayer(rainlayer);
  }

  // 创建有雨雨量站HTML图层
  let rainYesHtmlLayer = viewer.getLayer("rain-yes-layer-html");
  if (rainYesHtmlLayer) {
    rainYesHtmlLayer.clear();
  } else {
    rainYesHtmlLayer = new DC.HtmlLayer("rain-yes-layer-html");
    window.viewer.addLayer(rainYesHtmlLayer);
  }

  // 创建无雨雨量站HTML图层（默认隐藏）
  let rainNoHtmlLayer = viewer.getLayer("rain-no-layer-html");
  if (rainNoHtmlLayer) {
    rainNoHtmlLayer.clear();
  } else {
    rainNoHtmlLayer = new DC.HtmlLayer("rain-no-layer-html");
    window.viewer.addLayer(rainNoHtmlLayer);
  }

  obj2.forEach((item) => {
    item["nowTime"] = nowTime;
    item["beforeTime"] = beforeTime;

    // 根据降雨量分类添加到不同图层
    const isRaining = item.todayRainfall > 0;
    const targetLayer = isRaining ? rainYesLayer : rainNoLayer;
    const targetHtmlLayer = isRaining ? rainYesHtmlLayer : rainNoHtmlLayer;

    if (firstRainLoad.value) {
      addrainData(
        item,
        targetLayer,
        targetHtmlLayer,
        isRaining ? "rain-yes" : "rain-no"
      );
    } else if (!firstRainLoad.value) {
      addrainData(
        item,
        targetLayer,
        targetHtmlLayer,
        isRaining ? "rain-yes" : "rain-no"
      );
    }
  });
  state.raindata = obj2;
  firstRainLoad.value = false;

  // 默认隐藏无雨的雨量站
  changeVis({
    id: 51,
    label: "无雨",
    type: "rain-no",
    treeVisible: false,
  });
};
//河道站点位添加
const getRiverList = async (viewer) => {
  riverStationList({
    startTime: moment().format("YYYY-MM-DD HH:mm:ss"),
    pageNum: 1,
    pageSize: 99999,
  }).then((res) => {
    const obj = res.data;
    clearLayer("river", viewer);
    let layer = new DC.PrimitiveLayer("river-primitive");
    window.viewer.addLayer(layer);
    let hlayer = viewer.getLayer("river-layer-html");
    let list = [];
    obj.forEach((el) => {
      el.time = moment(el.time).format("MM-DD HH:mm");
      addriverData(el, layer, hlayer);
      list.push(el);
    });
    state.riverdata = list;
  });
};
const getRsvrList = async (viewer) => {
  let time = moment().format("YYYY-MM-DD HH:mm:ss");
  const res = await reservoirStationList({
    pageNum: 1,
    pageSize: 99999,
    time: time,
  });
  const obj = res.data.records || [];
  // console.log(obj, '水库水情数据')
  clearLayer("reservoir", viewer);
  // 创建点位
  let layer = new DC.PrimitiveLayer("reservoir-primitive");
  window.viewer.addLayer(layer);
  let hlayer = viewer.getLayer("reservoir-layer-html");
  let list = [];
  obj.forEach((item) => {
    item["time"] = moment(time).format("MM-DD HH:mm");
    addreservoirData(item, layer, hlayer);
    list.push(item);
  });
  state.reservoirdata = list;
};

const clearLayer = (name, viewer) => {
  if (viewer) {
    let layer = viewer.getLayer(name + "-layer");
    let htmllayer = viewer.getLayer(name + "-layer-html");
    layer?.clear();
    htmllayer?.clear();

    // 如果是雨量站图层，还需要清除分离的HTML图层
    if (name === "rain") {
      let rainYesHtmlLayer = viewer.getLayer("rain-yes-layer-html");
      let rainNoHtmlLayer = viewer.getLayer("rain-no-layer-html");
      rainYesHtmlLayer?.clear();
      rainNoHtmlLayer?.clear();
    }
  }
};
const addListeners = () => {
  window.EventBus.$on("rain/update", rainUpdate);
  window.EventBus.$on("river/update", riverUpdate);
  window.EventBus.$on("reservoir/update", reservoirUpdate);
  window.EventBus.$on("map/marker", checkMapMarker);
  window.EventBus.$on("map/marker/clear", clearMapMarker);
  window.EventBus.$on("module/type/change", changeModuleType);

  window.EventBus.$on("transfer/risk", (list) => {
    transferRisk(list);
  });
  window.EventBus.$on("transfer/place", (list) => {
    transferPlace(list);
  });

  // 调度方案的选择 之后允许地图上的河道水库的浮动框
  window.EventBus.$on("floodPreRehearsal/dispatch/select", dispatchSelect);
  window.EventBus.$on("river3/update", river3Update);
  window.EventBus.$on("reservoir3/update", reservoir3Update);
  window.EventBus.$on("river31/update", river31Update);
  window.EventBus.$on("reservoir31/update", reservoir31Update);
  window.EventBus.$on("changeAreaSelect", (data) => {
    if (data.type === "adcd") {
      adcd.value = data.code;
    } else if (data.type === "watershed") {
      state.lycode = data.code;
      getRiverData(window.leftViewer);
    }
  });
  //流域颜色渐变的监听事件
  window.EventBus.$on("catchmentFillColor_viewer/update", (color) => {
    catchmentFillColor_viewer = color;
  });
  window.EventBus.$on("catchmentFillColor_viewer2/update", (color) => {
    catchmentFillColor_viewer2 = color;
  });
};

// 危险点点位数据
const transferRisk = (list) => {
  if (list == null || list.length == 0) {
    let htmllayer = window.viewer.getLayer("risk-html");
    if (htmllayer) {
      window.viewer.removeLayer(htmllayer);
    }
  } else {
    let htmllayer1 = window.viewer.getLayer("risk-html");
    if (htmllayer1) {
      window.viewer.removeLayer(htmllayer1);
    }
    // let htmllayer = window.viewer.getLayer('risk-html')
    // htmllayer?.clear()
    let risklayer = new DC.HtmlLayer("risk-html");
    window.viewer.addLayer(risklayer);
    list.forEach((item) => {
      let divIcon = new DC.DivIcon(
        new DC.Position(item.adcdCenLon, item.adcdCenLat),
        `<div class='markerBox' onmouseover="showMorePopInfoRisk('${
          item.riskId
        }')" onmouseout="hideMorePopInfoRisk('${item.riskId}')">
              <div id="${
                item.riskId + "pop"
              }" class="marker-cont"  style="display: none">
                <div class='popupTitle'>${item.riskName}</div>
                <div class="no" style="${
                  item.transferStatus == 1 ? "" : "display: none;"
                }">
                  <div class='item'>
                    <div class='label'>总人口</div>
                    <div class='value'>${item.peoTotal}人</div>
                  </div>
                  <div class='item'>
                    <div class='label'>总户数</div>
                    <div class='value'>${item.households}户</div>
                  </div>
                  <div class='item'>
                    <div class='label'>致险原因</div>
                    <div class='value'>${item.riskReason}</div>
                  </div>
                  <div class='item'>
                    <div class='label'>发生时间</div>
                    <div class='value'>${item.occurrenceTime}</div>
                  </div>
                  <div class='item'>
                    <div class='label'>负责人</div>
                    <div class='value'>${
                      item.transferName ? item.transferName : ""
                    } ${item.transferTel}</div>
                  </div>
                </div>
                <div class="liji" style="${
                  item.transferStatus == 2 || item.transferStatus == 3
                    ? ""
                    : "display: none;"
                }">
                  <div class='item'>
                    <div class='label'>应转移</div>
                    <div class='value'>${item.shouldTransferCount}人</div>
                  </div>
                  <div class='item'>
                    <div class='label'>已转移</div>
                    <div class='value'>${item.alreadyTransferCount}人</div>
                  </div>
                  <div class='item'>
                    <div class='label'>未转移</div>
                    <div class='value'>${item.noTransferCount}人</div>
                  </div>
                  <div class='item'>
                    <div class='label'>致险原因</div>
                    <div class='value'>${item.riskReason}</div>
                  </div>
                  <div class='item'>
                    <div class='label'>发生时间</div>
                    <div class='value'>${item.occurrenceTime}</div>
                  </div>
                  <div class='item'>
                    <div class='label'>负责人</div>
                    <div class='value'>${
                      item.transferName ? item.transferName : ""
                    } ${item.transferTel}</div>
                  </div>
                  <div class='item'>
                    <div style="width:100px;" class='label'>开始转移时间</div>
                    <div class='value'>1-2 12:00</div>
                  </div>
                </div>
              </div>
              <div class="dangerMarker risk${item.riskLevel}${
          item.transferStatus
        }" ></div>
            </div>`
      );
      divIcon.attr = item;
      divIcon.on(DC.MouseEventType.CLICK, (e) => {
        window.EventBus.$emit("transfer/showRisk", {
          id: e.overlay.attr.riskTransferId,
          riskName: e.overlay.attr.riskName,
        });
      });
      risklayer.addOverlay(divIcon);
    });
  }
};
// 安置点点位数据
const transferPlace = (list) => {
  if (list == null || list.length == 0) {
    let htmllayer1 = window.viewer.getLayer("place-html");
    if (htmllayer1) {
      window.viewer.removeLayer(htmllayer1);
    }
  } else {
    let htmllayer2 = window.viewer.getLayer("place-html");
    if (htmllayer2) {
      window.viewer.removeLayer(htmllayer2);
    }
    let placelayer = new DC.HtmlLayer("place-html");
    window.viewer.addLayer(placelayer);
    list.forEach((item) => {
      let divIcon = new DC.DivIcon(
        new DC.Position(item.cenLon, item.cenLat),
        `<div class='markerBox' onmouseover="showMorePopInfoPlace('${
          item.placeId
        }')" onmouseout="hideMorePopInfoPlace('${item.placeId}')">
            <div id="${
              item.placeId + "pop"
            }" class="marker-cont placeBox"  style="display: none">
              <div class='popupTitle'>${item.placeName}</div>
              <div class='item'>
                <div class='label'>已安置</div>
                <div class='value'>${
                  item.alreadyPeoNum ? item.alreadyPeoNum : 0
                }人</div>
              </div>
              <div class='item'>
                <div class='label'>可安置</div>
                <div class='value'>${item.peoNum}人</div>
              </div>
              <div class='item'>
                <div class='label'>联系人</div>
                <div class='value'>${item.peoName} ${item.peoTel}</div>
              </div>
            </div>
            <div class="placeMarker" ></div>
          </div>`
      );
      divIcon.attr = item;
      divIcon.on(DC.MouseEventType.CLICK, (e) => {
        window.EventBus.$emit("transfer/showPlace", {
          id: e.overlay.attr.placeId,
        });
      });
      placelayer.addOverlay(divIcon);
    });
  }
  // 定义一个名为 source 的变量，存储了一段 GLSL 代码字符串
  let source = `czm_material czm_getMaterial(czm_materialInput materialInput)
            {
                // 获取默认材质
                czm_material material = czm_getDefaultMaterial(materialInput);
                // 获取材质输入的纹理坐标
                vec2 st = materialInput.st;
                // 使用纹理坐标和帧数计算动画效果，并获取纹理颜色
                vec4 colorImage = texture(image, vec2(fract((st.s  - speed * czm_frameNumber * 0.001)), st.t));
                // 设置材质的透明度，基于纹理颜色的 alpha 通道和外部传入的 alpha 值
                material.alpha = colorImage.a * color.a;
                // 设置材质的漫反射颜色，基于外部传入的颜色值
                material.diffuse = color.rgb;
                // 返回最终材质
                return material;
        }`;
  let Cesium = DC.getLib("Cesium");
  let material = new Cesium.Material({
    fabric: {
      uniforms: {
        color: Cesium.Color.fromCssColorString("#7ffeff"),
        image: "/icons/marker/jt.png",
        speed: 15,
      },
      source: source,
    },
    translucent: function () {
      return true;
    },
  });

  const appearance = new Cesium.PolylineMaterialAppearance();
  appearance.material = material;
  let array = [
    [115.911629, 37.175499, 116.035812, 37.182628],
    [115.911629, 37.175499, 115.871548, 37.198878],
    //赵六
    [115.986783, 37.323347, 115.990362, 37.284348],
    //孙八
    [115.986783, 37.323347, 116.109892, 37.199733],
  ];
  array.forEach((item) => {
    window.viewer.scene.primitives.add(
      new Cesium.Primitive({
        geometryInstances: new Cesium.GeometryInstance({
          geometry: new Cesium.PolylineGeometry({
            positions: Cesium.Cartesian3.fromDegreesArray(item),
            width: 25.0,
          }),
        }),
        appearance: appearance,
      })
    );
  });
};
// 添加河流数据
const getRiverData = async (viewer) => {
  try {
    let res = await getRiverGeoList({});
    let resRiver = res.rows || [];
    if (resRiver.length > 0) {
      try {
        // 1. 干流 LayerGroup
        let riverMainGroup = viewer.getLayerGroup("river-main-group");
        if (riverMainGroup) riverMainGroup.remove();
        riverMainGroup = new DC.LayerGroup("river-main-group");
        viewer.addLayerGroup(riverMainGroup);
        let riverMainLabels = new Cesium.LabelCollection();
        riverMainGroup._labels = riverMainLabels; // 便于后续管理
        viewer.scene.primitives.add(riverMainLabels);

        // 2. 一级支流 LayerGroup
        let river1stGroup = viewer.getLayerGroup("river-branch-1st-group");
        if (river1stGroup) river1stGroup.remove();
        river1stGroup = new DC.LayerGroup("river-branch-1st-group");
        viewer.addLayerGroup(river1stGroup);
        let river1stLabels = new Cesium.LabelCollection();
        river1stGroup._labels = river1stLabels;
        viewer.scene.primitives.add(river1stLabels);

        // 3. 二级支流 LayerGroup
        let river2ndGroup = viewer.getLayerGroup("river-branch-2nd-group");
        if (river2ndGroup) river2ndGroup.remove();
        river2ndGroup = new DC.LayerGroup("river-branch-2nd-group");
        viewer.addLayerGroup(river2ndGroup);
        let river2ndLabels = new Cesium.LabelCollection();
        river2ndGroup._labels = river2ndLabels;
        viewer.scene.primitives.add(river2ndLabels);

        // 颜色配置（行业标准蓝色分级）
        // 干流：#1E90FF（DodgerBlue，亮蓝，突出）
        // 一级支流：#00BFFF（DeepSkyBlue，次亮蓝）
        // 二级支流：#87CEFA（LightSkyBlue，淡蓝）
        let colors = [
          DC.Color.fromCssColorString("#1E90FF"), // 干流
          DC.Color.fromCssColorString("#00BFFF"), // 一级支流
          DC.Color.fromCssColorString("#87CEFA"), // 二级支流
        ];

        // 4. 分类添加河流要素和label
        resRiver.forEach((item) => {
          if (item.geom) {
            let riverGeoJson =
              typeof item.geom === "string" ? JSON.parse(item.geom) : item.geom;
            let group = null;
            let labelCollection = null;
            let colorIdx = 0;
            let width = 1;
            let font = "14px KaiTi, Microsoft YaHei, Arial, sans-serif";
            if (item.rvGrad == "0") {
              group = riverMainGroup;
              labelCollection = riverMainLabels;
              colorIdx = 0;
              width = 4;
              font = "24px KaiTi, Microsoft YaHei, Arial, sans-serif";
            } else if (item.rvGrad == "1") {
              group = river1stGroup;
              labelCollection = river1stLabels;
              colorIdx = 1;
              width = 2;
              font = "18px KaiTi, Microsoft YaHei, Arial, sans-serif";
            } else if (item.rvGrad == "2") {
              group = river2ndGroup;
              labelCollection = river2ndLabels;
              colorIdx = 2;
              width = 1;
              font = "14px KaiTi, Microsoft YaHei, Arial, sans-serif";
            } else {
              return; // 未知等级不处理
            }
            // 添加GeoJson要素
            let riverLayer = new DC.GeoJsonLayer(
              `riverlayer-${item.rvGrad}-${item.id}`,
              riverGeoJson
            );
            group.addLayer(riverLayer);
            // 设置样式
            riverLayer.eachOverlay((overlay) => {
              if (overlay.polyline) {
                DC.Util.merge(overlay.polyline, {
                  material: new DC.ColorMaterialProperty(colors[colorIdx]),
                  clampToGround: false,
                  width: width,
                });
                // 添加label到对应LabelCollection
                if (
                  overlay.polyline &&
                  Array.isArray(overlay.polyline.positions._value) &&
                  overlay.polyline.positions._value.length > 0
                ) {
                  const midIndex = Math.floor(
                    overlay.polyline.positions._value.length / 2
                  );
                  const midPosition =
                    overlay.polyline.positions._value[midIndex];
                  labelCollection.add({
                    position: midPosition,
                    text: item.rvName,
                    font: font,
                    fillColor: DC.Color.WHITE,
                    outlineColor: DC.Color.fromCssColorString(
                      "rgba(29, 190, 254, 1)"
                    ),
                    outlineWidth: 5,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    verticalOrigin: DC.VerticalOrigin.BOTTOM,
                    pixelOffset: new DC.Cartesian2(0, -10),
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    // 仅二级支流label添加distanceDisplayCondition
                    ...(item.rvGrad == "2"
                      ? {
                          distanceDisplayCondition: {
                            near: 0, //最近距离
                            far: 200000, //最远距离
                          },
                        }
                      : {}),
                  });
                }
              }
            });
          }
        });

        // 5. LayerGroup.show联动控制
        const syncGroupShow = (group, labelCollection) => {
          Object.defineProperty(group, "show", {
            set(val) {
              // 控制所有子layer的show
              if (group.getLayers) {
                const layers = group.getLayers();
                if (Array.isArray(layers)) {
                  layers.forEach((l) => (l.show = val));
                }
              }
              if (labelCollection) labelCollection.show = val;
            },
            get() {
              // 只要有一个子layer是显示的就算显示
              if (group.getLayers) {
                const layers = group.getLayers();
                if (Array.isArray(layers)) {
                  return layers.some((l) => l.show);
                }
              }
              return false;
            },
          });
        };
        syncGroupShow(riverMainGroup, riverMainLabels);
        syncGroupShow(river1stGroup, river1stLabels);
        syncGroupShow(river2ndGroup, river2ndLabels);
      } catch (e) {
        console.error(e);
      }
    }
  } catch (e) {
    console.error(e);
  }
};

// 添加底图的区县级数据基础业务
const getAreaGeoData = async (viewer) => {
  let Cesium = DC.getLib("Cesium");
  // 1. 获取 geojson
  const code = "540100_full";
  const { data: geojson } = await getAliyunGeoJsonByCode(code);

  // 2. 清理旧图层（用 viewer 属性存储，避免重复添加）
  if (viewer._areaPolygonPrimitive) {
    viewer.scene.primitives.remove(viewer._areaPolygonPrimitive);
    viewer._areaPolygonPrimitive = null;
  }
  if (viewer._areaPolylinePrimitive) {
    viewer.scene.primitives.remove(viewer._areaPolylinePrimitive);
    viewer._areaPolylinePrimitive = null;
  }
  if (viewer._areaLabelCollection) {
    viewer.scene.primitives.remove(viewer._areaLabelCollection);
    viewer._areaLabelCollection = null;
  }

  // 3. 创建多边形和边框实例
  const polygonInstances = [];
  const polylineInstances = [];
  const fillColor = Cesium.Color.fromCssColorString("rgba(180,180,180,0.3)");
  const borderColor = Cesium.Color.fromCssColorString("rgba(186,246,8,0.9)");
  const borderWidth = 1.7;
  geojson.features.forEach((feature) => {
    const { geometry } = feature;
    const polygons =
      geometry.type === "Polygon"
        ? [geometry.coordinates]
        : geometry.coordinates;
    polygons.forEach((polygon) => {
      polygon.forEach((ring) => {
        const positions = ring.map(([lng, lat]) =>
          Cesium.Cartesian3.fromDegrees(lng, lat)
        );
        // 多边形面填充
        polygonInstances.push(
          new Cesium.GeometryInstance({
            geometry: new Cesium.PolygonGeometry({
              polygonHierarchy: new Cesium.PolygonHierarchy(positions),
            }),
            attributes: {
              color: Cesium.ColorGeometryInstanceAttribute.fromColor(fillColor),
            },
          })
        );
        // 多边形边界线
        polylineInstances.push(
          new Cesium.GeometryInstance({
            geometry: new Cesium.PolylineGeometry({
              positions: positions.concat([positions[0]]), // 闭合
              width: borderWidth,
            }),
          })
        );
      });
    });
  });

  // 添加多边形填充
  viewer._areaPolygonPrimitive = viewer.scene.primitives.add(
    new Cesium.Primitive({
      geometryInstances: polygonInstances,
      appearance: new Cesium.PerInstanceColorAppearance({
        closed: true,
        translucent: true,
      }),
    })
  );

  // 添加多边形边框（紫色虚线，虚实更明显）
  viewer._areaPolylinePrimitive = viewer.scene.primitives.add(
    new Cesium.Primitive({
      geometryInstances: polylineInstances,
      appearance: new Cesium.PolylineMaterialAppearance({
        material: Cesium.Material.fromType("PolylineDash", {
          color: borderColor,
          dashLength: 16.0,
          dashPattern: 255,
          gapColor: Cesium.Color.TRANSPARENT,
        }),
      }),
    })
  );

  // label黑色填充+白色描边
  viewer._areaLabelCollection = new Cesium.LabelCollection();
  geojson.features.forEach((feature) => {
    const name = feature.properties.name;
    const center = turf.centerOfMass(feature);
    const [lng, lat] = center.geometry.coordinates;
    // 判断是否为"堆龙德庆区"
    const pixelOffset =
      name === "堆龙德庆区"
        ? new Cesium.Cartesian2(-120, -6)
        : new Cesium.Cartesian2(0, -6);

    viewer._areaLabelCollection.add({
      position: Cesium.Cartesian3.fromDegrees(lng, lat),
      text: name,
      font: '24px KaiTi, "Microsoft YaHei", Arial, sans-serif',
      fillColor: DC.Color.BLACK,
      outlineColor: DC.Color.WHITE,
      outlineWidth: 8,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      verticalOrigin: Cesium.VerticalOrigin.CENTER,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      pixelOffset,
      translucencyByDistance: new Cesium.NearFarScalar(1000, 1.0, 2000000, 0.2),
    });
  });
  viewer.scene.primitives.add(viewer._areaLabelCollection);
};

const rrrUpdate = (type, list, skip) => {
  clearLayer(type, window.viewer);
  let layer = window.viewer.getLayer(type + "-layer");
  let hlayer = window.viewer.getLayer(type + "-layer-html");
  let type2 = type.replace("2", "");
  state[type2 + "data"] = list;
  list.forEach((item) => {
    try {
      addDataFunc["add" + type2 + "Data"](item, layer, hlayer);
      if (!skip && state.curStData && item.STCD === state.curStData.STCD) {
        // 如果有关键字的搜索了之前，那么也需要刷新一下
        checkMapMarker(item);
      }
    } catch (e) {}
  });

  // 不加这个有时候有bug
  // window.viewer.getLayerGroup(type).show = window.viewer.getLayerGroup(type).show
  window.viewer.getLayerGroup(type).show = true;
};
const rrr2Update = (type, list, skip) => {
  if (window.rightViewer) {
    clearLayer(type, window.rightViewer);
    let layer = window.rightViewer.getLayer(type + "-layer");
    let hlayer = window.rightViewer.getLayer(type + "-layer-html");
    let type2 = type.replace("1", "");
    state[type + "data"] = list;
    list.forEach((item) => {
      try {
        addDataFunc["add" + type2 + "Data"](item, layer, hlayer);
        // 下边先隐藏，乱套了不然
        // if(!skip && state.curStData && item.STCD === state.curStData.STCD) {
        //   // 如果有关键字的搜索了之前，那么也需要刷新一下
        //   checkMapMarker(item)
        // }
      } catch (e) {}
    });

    // 不加这个有时候有bug
    // window.viewer.getLayerGroup(type).show = window.viewer.getLayerGroup(type).show
    window.rightViewer.getLayerGroup(type).show = true;
  }
};
const rainUpdate = (list) => {
  // 清除现有图层
  clearLayer("rain", window.viewer);

  // 获取或创建分类图层
  let rainYesLayer = window.viewer.getLayer("rain-yes-primitive");
  if (rainYesLayer) {
    rainYesLayer.clear();
  } else {
    rainYesLayer = new DC.PrimitiveLayer("rain-yes-primitive");
    window.viewer.addLayer(rainYesLayer);
  }

  let rainNoLayer = window.viewer.getLayer("rain-no-primitive");
  if (rainNoLayer) {
    rainNoLayer.clear();
  } else {
    rainNoLayer = new DC.PrimitiveLayer("rain-no-primitive");
    rainNoLayer.show = false; // 默认隐藏无雨雨量站
    window.viewer.addLayer(rainNoLayer);
  }

  // 创建有雨雨量站HTML图层
  let rainYesHtmlLayer = window.viewer.getLayer("rain-yes-layer-html");
  if (rainYesHtmlLayer) {
    rainYesHtmlLayer.clear();
  } else {
    rainYesHtmlLayer = new DC.HtmlLayer("rain-yes-layer-html");
    window.viewer.addLayer(rainYesHtmlLayer);
  }

  // 创建无雨雨量站HTML图层（默认隐藏）
  let rainNoHtmlLayer = window.viewer.getLayer("rain-no-layer-html");
  if (rainNoHtmlLayer) {
    rainNoHtmlLayer.clear();
  } else {
    rainNoHtmlLayer = new DC.HtmlLayer("rain-no-layer-html");
    rainNoHtmlLayer.show = false; // 默认隐藏无雨雨量站HTML
    window.viewer.addLayer(rainNoHtmlLayer);
  }

  // 分类添加雨量站
  state.raindata = list;
  list.forEach((item) => {
    try {
      const isRaining = item.todayRainfall > 0;
      const targetLayer = isRaining ? rainYesLayer : rainNoLayer;
      const targetHtmlLayer = isRaining ? rainYesHtmlLayer : rainNoHtmlLayer;
      addrainData(
        item,
        targetLayer,
        targetHtmlLayer,
        isRaining ? "rain-yes" : "rain-no"
      );
    } catch (e) {
      console.error("Error adding rain data:", e);
    }
  });

  // 控制图层显示状态
  window.viewer.getLayerGroup("rain").show = true;
};
const riverUpdate = (list) => {
  rrrUpdate("river", list);
};
const reservoirUpdate = (list) => {
  rrrUpdate("reservoir", list);
};

const river3Update = (list) => {
  rrrUpdate("river3", list);
};
const reservoir3Update = (list) => {
  rrrUpdate("reservoir3", list);
};

const river31Update = (list) => {
  rrr2Update("river31", list);
};
const reservoir31Update = (list) => {
  rrr2Update("reservoir31", list);
};
const mapLayerMaps = {
  PP: "rain",
  RR: "reservoir",
  ZZ: "river",
  ZQ: "river",
  // 为了给防洪预演（对比分析用）
  RR3: "reservoir3",
  ZZ3: "river3",
  ZQ3: "river3",
  RR31: "reservoir31",
  ZZ31: "river31",
  ZQ31: "river31",
  TOWNSHIP: "township", // 添加村社图层映射
  ENTERPRISE: "enterprise",
  RESIDENT: "resident",
  RISK_PROBLEM: "risk-problem",
  PLACE: "place",
  DANGER_AREA: "dangerArea", // 添加危险区图层映射
  VIDEO: "video-monitoring", // 添加视频监控图层映射
};
// 清除定位点
const clearMapMarker = () => {
  for (let key in mapLayerMaps) {
    let type = mapLayerMaps[key];
    rrrUpdate(type + "2", []);
  }
  state.curStData = null;
};
// 关闭漳渭河的点图层
const closeZwhPoi = (viewer) => {
  let FloodDetentionArea = viewer.getLayerGroup("FloodDetentionArea");
  if (FloodDetentionArea) {
    FloodDetentionArea.show = false;
  }
  if (viewer.entities.values.length > 0) {
    viewer.entities.values.forEach((item) => {
      if (item.id == "exw" || item.id == "dmfq" || item.id == "snsz") {
        item.show = false;
      }
    });
  }
};
// 头部模块切换
const changeModuleType = async (data) => {
  currentType.value = data.name;
  window.EventBus.$emit("change/right/show");
  if (data.name === "防洪预演") {
    // window.EventBus.$emit('gotoTab', { id: 0 })
    window.viewer.getLayerGroup("rain").show = false;
    window.viewer.getLayerGroup("river").show = true;
    window.viewer.getLayerGroup("reservoir").show = true;
    window.EventBus.$emit("colorBandShow/update", false);
  } else if (data.name === "避险转移") {
    // window.EventBus.$emit('gotoTab', { id: 0 })
    window.viewer.getLayerGroup("rain").show = false;
    window.viewer.getLayerGroup("river").show = false;
    window.viewer.getLayerGroup("reservoir").show = false;
    //将防洪预演的河流隐藏, river1恢复显示
    window.viewer.getLayerGroup("river1").show = true;

    window.viewer.getLayerGroup("river_zwh")
      ? (window.viewer.getLayerGroup("river_zwh").show = false)
      : null;
    //分屏恢复
    // window.viewer.getLayerGroup('river_zwh_1') ? window.viewer.getLayerGroup('river_zwh_1').show = false : null
    window.viewer2.getLayerGroup("river_zwh_2")
      ? (window.viewer2.getLayerGroup("river_zwh_2").show = false)
      : null;
    window.EventBus.$emit("colorBandShow/update", false);
    closeZwhPoi(window.viewer);
  } else if (data.name === "对比分析") {
    // window.EventBus.$emit('change/right/hide')
    //地图自适应大小
    if (window["viewer2"]) {
      // 如果创建过了那就显示刷新数据
      console.log("创建过了");
    } else {
      let viewer = await createViewer("viewer2", "map3d2");
      window.rightViewer = viewer;
      getRsvrList(viewer);
    }
  } else if (currentType.value === "态势分析") {
    window.viewer.getLayerGroup("rain").show = true;
    window.viewer.getLayerGroup("river").show = true;
    //将防洪预演的河流隐藏, river1恢复显示
    window.viewer.getLayerGroup("river1").show = true;
    window.viewer.getLayerGroup("river_zwh")
      ? (window.viewer.getLayerGroup("river_zwh").show = false)
      : null;
    //分屏恢复
    // window.viewer.getLayerGroup('river_zwh_1') ? window.viewer.getLayerGroup('river_zwh_1').show = false : null
    window.viewer2?.getLayerGroup("river_zwh_2")
      ? (window.viewer2.getLayerGroup("river_zwh_2").show = false)
      : null;
    window.viewer.getLayerGroup("reservoir").show = true;
    window.EventBus.$emit("gotoTab", { id: 0 });
    window.EventBus.$emit("colorBandShow/update", false);
    closeZwhPoi(window.viewer);
  }
  if (data.name != "避险转移") {
    let htmllayer = window.viewer.getLayer("risk-html");
    // htmllayer?.clear()
    if (htmllayer) {
      window.viewer.removeLayer(htmllayer);
    }
    let htmllayer1 = window.viewer.getLayer("place-html");
    if (htmllayer1) {
      window.viewer.removeLayer(htmllayer1);
    }
    // console.log(window.viewer.scene.primitives);
    window.viewer.scene.primitives._primitives.forEach((primitive) => {
      if (!primitive._guid) {
        primitive.show = false;
      }
    });
  }
  if (data.name != "态势分析") {
    let gLayer = window.viewer.getLayerGroup("flood-warn-group");
    gLayer.remove();
  }
};
// 调度方案选择之后的处理
const dispatchSelect = () => {};
const checkMapMarker = (data) => {
  let layerName = mapLayerMaps[data.STTP];
  if (layerName) {
    let layer = viewer.getLayer(layerName + "-layer");
    let hasIt = false;
    let attr = null;
    layer.getOverlays().forEach((item) => {
      if (item.attr?.STCD === data.STCD) {
        hasIt = true;
        attr = item.attr;
      }
    });
    if (hasIt) {
      // 没有就增加
      state.curStData = attr;
      rrrUpdate(layerName + "2", [attr], true);
    } else {
      state.curStData = data;
      rrrUpdate(layerName + "2", [data], true);
    }
  }
};

const createGLayer = (type) => {
  let hasLayer = viewer.getLayer(type + "-layer");
  if (hasLayer) {
    return;
  }
  let layer = new DC.VectorLayer(type + "-layer");
  let layer2 = new DC.VectorLayer(type + "2-layer");
  let hlayer = new DC.HtmlLayer(type + "-layer-html");
  let hlayer2 = new DC.HtmlLayer(type + "2-layer-html");
  let glayer = new DC.LayerGroup(type);
  let glayer2 = new DC.LayerGroup(type + "2");
  viewer.addLayerGroup(glayer);
  viewer.addLayerGroup(glayer2);
  glayer.addLayer(layer);
  glayer.addLayer(hlayer);
  glayer2.addLayer(layer2);
  glayer2.addLayer(hlayer2);
};

const getCamera = () => {
  let Cesium = DC.getLib("Cesium");
  const camera = window.viewer.scene.camera;
  const cartographic = Cesium.Cartographic.fromCartesian(camera.position);
  const x = Cesium.Math.toDegrees(cartographic.longitude);
  const y = Cesium.Math.toDegrees(cartographic.latitude);
  const z = cartographic.height;
  let pt = Cesium.Cartographic.fromDegrees(x, y, z);
  let ellipsoid = viewer.scene.globe.ellipsoid;
  let cartesian3 = ellipsoid.cartographicToCartesian(pt);
  let objinfo = {
    经度: x,
    维度: y,
    高度: z,
    x: cartesian3.x,
    y: cartesian3.y,
    z: cartesian3.z,
    heading: camera.heading,
    pitch: camera.pitch,
    roll: camera.roll,
  };
  console.log(objinfo);
};

window.getCamera = getCamera;

// 创建主地图
const createViewer = async (name, id) => {
  Cesium = DC.getLib("Cesium");
  viewer = new DC.Viewer(id, {
    // 解决浏览器缩放后场景变形模糊的问题 useBrowserRecommendedResolution如果为 true 模糊， false 清晰， 好使
    useBrowserRecommendedResolution: false,
    // scene3DOnly: false, // 改为false以支持2D模式
    // sceneMode: 2, // 设置初始场景模式为2D (Cesium.SceneMode.SCENE2D)
    // sceneModePicker: false, // 不显示场景模式切换按钮
    skyAtmosphere: false,
    contextOptions: {
      webgl: {
        alpha: true,
      },
    },
  });
  viewer.scene.sun.show = false;
  viewer.scene.moon.show = false;
  viewer.scene.skyBox.show = false; //关闭天空盒，否则会显示天空颜色
  viewer.scene.undergroundMode = true;
  viewer.scene.globe.show = true;

  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 1000;
  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 5600000;
  if (Cesium.FeatureDetection.supportsImageRenderingPixelated()) {
    //判断是否支持图像渲染像素化处理
    viewer.resolutionScale = window.devicePixelRatio;
  }
  viewer.scene.fxaa = true;
  viewer.scene.postProcessStages.fxaa.enabled = true;

  //新加一个zwhdem测试
  // let terrain = DC.TerrainFactory.createUrlTerrain({
  //   url: '/zwh_dem30m',
  // })
  // viewer.setTerrain(terrain)
  //用于夸大地形的标量。默认为1.0（不夸张）。值2.0将地形缩放 2 倍。的值0.0使地形完全平坦。请注意，地形夸大不会修改任何其他图元，因为它们是相对于椭圆体定位的。
  viewer.scene.globe.terrainExaggeration = 2;
  //夸大地形的高度。默认为0.0（相对于椭球表面缩放）。高于此高度的地形将向上缩放，低于此高度的地形将向下缩放。请注意，地形夸大不会修改任何其他图元，因为它们是相对于椭圆体定位的。如果Globe#terrainExaggeration是1.0这个值将没有效果。
  viewer.scene.globe.terrainExaggerationRelativeHeight = 1.0;

  /* 添加在线的全球arcgis地形服务 */
  // try {
  //   viewer.scene.terrainProvider =
  //     await Cesium.ArcGISTiledElevationTerrainProvider.fromUrl(
  //       "https://elevation3d.arcgis.com/arcgis/rest/services/WorldElevation3D/Terrain3D/ImageServer",
  //     );
  // } catch (error) {
  //   window.alert(`Failed to load terrain. ${error}`);
  // }

  window[name] = viewer; // 挂载到window全局
  // viewer.mapCover.enable = true // 启用地图四周遮罩
  // viewer.locationBar.enable = true // 启用地图位置显示
  // viewer.compass.enable = true // 罗盘显示
  // viewer.zoomController.enable = true // 放大缩小和复位
  // // 是否支持图像渲染像素化处理
  Cesium = DC.getLib("Cesium");
  // Cesium.Camera.DEFAULT_VIEW_RECTANGLE = Cesium.Rectangle.fromDegrees(112.0, 38.5, 124.9,43.3);
  // if (Cesium.FeatureDetection.supportsImageRenderingPixelated()) {
  //   viewer.resolutionScale = window.devicePixelRatio
  // }

  // viewer.scene.globe.maximumScreenSpaceError = 4 / 3

  // 开启深度检测，不同的级别需要动态控制
  viewer.scene.globe.depthTestAgainstTerrain = true;

  let china_td_yingxiang = DC.ImageryLayerFactory.createImageryLayer(
    DC.ImageryType.TDT,
    {
      //天地影像图
      url: `http://t0.tianditu.gov.cn/img_c/wmts?tk=`,
      key: `86dc8be8c8a491d9abdf50a40b111862`,
      style: "img",
    }
  );
  // let china_td_yingxiang_meta = DC.ImageryLayerFactory.createImageryLayer(
  //   DC.ImageryType.TDT,
  //   {
  //     //天地影像图标记
  //     url: `http://t0.tianditu.gov.cn/cia_w/wmts?tk=`,
  //     key: `86dc8be8c8a491d9abdf50a40b111862`,
  //     style: "cva",
  //   }
  // );
  let china_td_dixing = DC.ImageryLayerFactory.createImageryLayer(
    DC.ImageryType.TDT,
    {
      //天地地形图
      url: `http://t0.tianditu.gov.cn/ter_w/wmts?tk=`,
      key: `86dc8be8c8a491d9abdf50a40b111862`,
      style: "ter",
    }
  );
  let china_td_dixing_meta = DC.ImageryLayerFactory.createImageryLayer(
    DC.ImageryType.TDT,
    {
      //天地地形图标记
      url: `http://t0.tianditu.gov.cn/cta_w/wmts?tk=`,
      key: `86dc8be8c8a491d9abdf50a40b111862`,
      style: "cta",
    }
  );
  let china_td_shiliang = DC.ImageryLayerFactory.createImageryLayer(
    DC.ImageryType.TDT,
    {
      //天地矢量图
      url: `http://t0.tianditu.gov.cn/vec_w/wmts?tk=`,
      key: `86dc8be8c8a491d9abdf50a40b111862`,
      style: "vec",
    }
  );
  let china_td_shiliang_meta = DC.ImageryLayerFactory.createImageryLayer(
    DC.ImageryType.TDT,
    {
      //天地矢量图标记
      url: `http://t0.tianditu.gov.cn/cva_w/wmts?tk=`,
      key: `86dc8be8c8a491d9abdf50a40b111862`,
      style: "cva",
    }
  );
  viewer.addBaseLayer([china_td_yingxiang], {
    name: "影像",
    iconUrl: img_yingxiang,
  });
  viewer.addBaseLayer([china_td_shiliang, china_td_shiliang_meta], {
    name: "矢量",
    iconUrl: img_ter,
  });
  viewer.addBaseLayer([china_td_dixing, china_td_dixing_meta], {
    name: "地形",
    iconUrl: img_elec,
  });

  viewer.mapSwitch.hide();
  for (let key in mapLayerMaps) {
    let type = mapLayerMaps[key];
    createGLayer(type);
  }

  // 创建租户的流域或顶级地理空间
  createWatershedLayer(viewer, "viewer");

  return viewer;
};
//底图挖孔 添加遮罩
const createGroundPrimitive = async (viewer, geom) => {
  //加载该租户下的可变区域geojson
  try {
    const geojsonData = geom;
    if (geojsonData) {
      const arr = geojsonData.features[0].geometry.coordinates[0][0];
      //turf获取多边形中心点
      const center = turf.centerOfMass(geojsonData);
      // console.log(center);
      const maskpointArray = [];
      for (let i = 0, l = arr.length; i < l; i++) {
        maskpointArray.push(arr[i][0]);
        maskpointArray.push(arr[i][1]);
      }
      // 将其转换成下边渲染entity所需的3D笛卡尔坐标系。
      let maskspoint = Cesium.Cartesian3.fromDegreesArray(maskpointArray);

      const clampline = new DC.Polyline(maskspoint);
      clampline.setStyle({
        width: 2,
        material: new DC.PolylineTrailMaterialProperty({
          color: DC.Color.fromCssColorString("rgba(32,201,255,0.8)"),
          speed: 3,
          percent: 0.3,
          gradient: 0.01,
        }),
      });
      // 添加发光效果底层
      const glowLine = new DC.Polyline(maskspoint);
      glowLine.setStyle({
        width: 8,
        material: DC.Color.fromCssColorString("rgba(32,201,255,0.2)"),
      });
      const globalMaskGrid = new DC.Polygon(
        Cesium.Cartesian3.fromDegreesArray([1, 0, 1, 89, 180, 89, 180, 0])
      );

      // 创建图片材质展示网格效果
      const imageMaterial = new Cesium.ImageMaterialProperty({
        image: screenMark,
        repeat: new Cesium.Cartesian2(1000, 2000), // 调整x和y方向的比例，补偿投影变形
        color: new Cesium.Color(1.0, 1.0, 1.0, 0.8), // 可调整透明度
        transparent: true,
      });

      globalMaskGrid.holes = [maskspoint];
      globalMaskGrid.setStyle({
        material: imageMaterial,
      });
      // 添加电子围栏墙体
      const wall = new DC.Wall(maskspoint);
      wall.setStyle({
        material: new DC.WallTrailMaterialProperty({
          color: DC.Color.fromCssColorString("rgba(32,201,255,0.3)"),
          speed: 10,
          percent: 0.15,
          gradient: 0.2,
        }),
        height: 3000,
        outline: true,
        outlineColor: DC.Color.fromCssColorString("rgba(32,201,255,0.4)"),
      });
      const maskLayer = new DC.VectorLayer("global-mask-layer");
      viewer.addLayer(maskLayer);
      maskLayer.addOverlay(glowLine);
      maskLayer.addOverlay(clampline);
      maskLayer.addOverlay(wall);

      maskLayer.addOverlay(globalMaskGrid);
      // 设置初始位置为 maskLayer
      viewer.camera.setView({
        destination: Cesium.Cartesian3.fromDegrees(
          center.geometry.coordinates[0],
          center.geometry.coordinates[1],
          550000 // 设置高度为150000米，可以根据需要调整
        ),
        orientation: {
          heading: Cesium.Math.toRadians(0), // 方向角设为0，正北方向
          pitch: Cesium.Math.toRadians(-90), // 俯仰角设为-90度，实现正俯视
          roll: 0, // 翻滚角设为0
        },
      });
    } else {
      console.log("No geojson data found");
    }
  } catch (e) {
    console.log(e);
  }
};
// 封装各租户的流域或顶级地理空间
const createWatershedLayer = async (viewer, name) => {
  // 查询后台
  let params = {
    lycode: "",
  };
  // 获取流域code
  let res2 = await selectStlyList(params);
  let watersheds = formatTree(res2.data);
  state.lycode = watersheds[0].basinId || null;
  // 获取流域边界
  const res = await selectStlyInfo(state.lycode);
  let geom =
    typeof res.data.geom == "string"
      ? JSON.parse(res.data.geom)
      : res.data.geom;
  if (geom.features?.length === 0) return;
  // 创建挖孔
  createGroundPrimitive(viewer, geom);
  let layer = new DC.GeoJsonLayer("layer", geom, {
    // stroke: DC.Color.YELLOW,
    // stroke: DC.Color.fromCssColorString("rgba(83,179,231,0.7)"),

    //9.26 通过流量控制流域颜色
    // fill: DC.Color.fromCssColorString("rgba(83,179,231,0.7)"),
    fill: new Cesium.CallbackProperty(() => {
      return name == "viewer"
        ? catchmentFillColor_viewer
        : catchmentFillColor_viewer2;
    }, false),
    strokeWidth: 5,
  });
  const centralPoint = turf.centerOfMass(geom);
  // 创建标签
  labels.add({
    position: new Cesium.Cartesian3.fromDegrees(
      centralPoint.geometry.coordinates[0] - 0.1, //手动校准 标签位置
      centralPoint.geometry.coordinates[1] - 0.05, //手动校准 标签位置
      0
    ),
    text: res.data.name, //流域名称
    font: "22px sans-serif",
    fillColor: DC.Color.WHITE,
    outlineColor: DC.Color.fromCssColorString("#f79f4c"),
    outlineWidth: 5,
    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
    verticalOrigin: DC.VerticalOrigin.BOTTOM,
    pixelOffset: new DC.Cartesian2(0, -10),
    disableDepthTestDistance: Number.POSITIVE_INFINITY,
  });

  viewer.addLayer(layer); // 为了获取边界视角定位，现在隐藏
  // viewer.flyTo(layer, 3);
  setTimeout(() => {
    // 读取layer里面的数据
    layer.eachOverlay((item) => {
      // item 为一个entity,
      if (item.polyline) {
        //todo
        let polyline = DC.Polyline.fromEntity(item);
      }
      if (item.polygon) {
        let boundaryLayer = new DC.VectorLayer("boundary-layer");
        let boundaryGroupLayer = new DC.LayerGroup("boundary");
        viewer.addLayerGroup(boundaryGroupLayer);
        boundaryGroupLayer.addLayer(boundaryLayer);
        let polygon = DC.Polygon.fromEntity(item);
        let extrudedHeight = 500;
        polygon.setStyle({
          // material: DC.Color.BLUE,
          material: DC.Color.fromCssColorString("rgba(69,140,73,1)"),
          // material:DC.Color.fromCssColorString("rgb(186,246,8"),
          // extrudedHeight: extrudedHeight,
          closeTop: false,
          closeBottom: true,
          fill: false, // 挤出高度的面是否填充
          outline: false,
          outlineColor: DC.Color.fromCssColorString("rgb(186,246,8)"), //边框颜色
          outlineWidth: 1, //边框宽度
          // arcType: Cesium.ArcType.RHUMB,
        });
        boundaryLayer.addOverlay(polygon);
        // 加边 光线
        let now = DC.JulianDate.now();
        let positions = DC.Transform.transformCartesianArrayToWGS84Array(
          item.polygon.hierarchy.getValue(now).positions
        );
        let pss = "";
        let pss2 = "";
        positions.map((item) => {
          // item.alt = 0
          pss += item.lng + "," + item.lat + ";";
          pss2 += item.lng + "," + item.lat + "," + extrudedHeight + ";";
        });
        pss = pss.substring(0, pss.length - 1);
        pss2 = pss2.substring(0, pss2.length - 1);
        let polygonAll = new DC.Polygon("66, 0;152, 0;172, 56;52, 56");
        polygonAll.holes = [pss];
        polygonAll.setStyle({
          extrudedHeight: extrudedHeight,
          material: DC.Color.fromCssColorString("rgba(10,102,136,0.1)"),
          outline: false,
          outlineColor: DC.Color.fromCssColorString("rgba(186,246,8,0.64)"), //边框颜色
          outlineWidth: 1, //边框宽度
        });
        boundaryLayer.addOverlay(polygonAll);

        let polygonAll2 = new DC.Polygon(pss);
        polygonAll2.setStyle({
          extrudedHeight: extrudedHeight,
          material: DC.Color.fromCssColorString("rgba(186,246,8,0.5)"),
          closeTop: false,
          closeBottom: false,
        });
        boundaryLayer.addOverlay(polygonAll2);
        let polylineTop = new DC.Polyline(pss2);
        polylineTop.setStyle({
          width: 2,
          // material: new DC.PolylineLightingMaterialProperty({
          //   color:  DC.Color.fromCssColorString("rgba(95,115,35,0.64)"),
          // }),
          material: DC.Color.fromCssColorString("rgb(133,176,5)"),
          clampToGround: false,
        });
        boundaryLayer.addOverlay(polylineTop);
      }
      if (item.billboard) {
        //todo
        let point = DC.Point.fromEntity(item);
        let divIcon = DC.DivIcon.fromEntity(item);
        let billboard = DC.Billboard.fromEntity(item);
      }
    });

    getRiverData(viewer);
    getAreaGeoData(viewer);
    // 添加各类图层
    for (let key in mapLayerMaps) {
      let type = mapLayerMaps[key];
      createGLayer(type);
    }

    let cloudLayer = new DC.VectorLayer("cloud-layer");
    viewer.addLayer(cloudLayer);
  }, 850);
};
// 回到one地图
const backOneMap = () => {
  // currentType.value = '防洪预演'
  window.EventBus.$emit("module/type/change", { name: "防洪预演" });
  window.EventBus.$emit("change/right/show");
  window.EventBus.$emit("backList");
  //隐藏两屏的河流river
  window.viewer.getLayerGroup("river_zwh").show = false;
  window.viewer2.getLayerGroup("river_zwh_2").show = false;
  window.viewer.getLayerGroup("river1").show = true;
  window.viewer2.getLayerGroup("river1").show = true;
  closeZwhPoi(window.viewer);
  closeZwhPoi(window.viewer2);
};

// 获取相机属性
const getViewPoint = (viewer) => {
  return {
    position: viewer.camera.position.clone(),
    orientation: {
      heading: viewer.camera.heading,
      pitch: viewer.camera.pitch,
      roll: viewer.camera.roll,
    },
  };
};
// 视窗移动,NowViewer就是鼠标现在在的，AfterViewer就是被联动那个
const ViewerMove = (NowViewer, AfterViewer) => {
  const viewPoint = getViewPoint(NowViewer);
  AfterViewer.camera.setView({
    destination: viewPoint.position,
    orientation: viewPoint.orientation,
  });
};

// 鼠标进入左边
const leftMouseEnter = () => {
  if (currentType.value != "对比分析") return;
  state.Handler = new Cesium.ScreenSpaceEventHandler(
    window.viewer.scene.canvas
  );
  state.Handler.setInputAction(() => {
    ViewerMove(window.viewer, window.viewer2);
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
};
// 鼠标从左边出去
const leftMouseLeave = () => {
  if (currentType.value != "对比分析") return;
  state.Handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK); //移除事件
};

// 鼠标进入右边
const rightMouseEnter = () => {
  if (currentType.value != "对比分析") return;
  state.Handler = new Cesium.ScreenSpaceEventHandler(
    window.viewer2.scene.canvas
  );
  state.Handler.setInputAction(() => {
    ViewerMove(window.viewer2, window.viewer);
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
};
// 鼠标从右边出去
const rightMouseLeave = () => {
  if (currentType.value != "对比分析") return;
  state.Handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK); //移除事件
};

onMounted(() => {
  DC.ready().then(async () => {
    // 增加地图监听
    addListeners();

    const viewer = await createViewer("viewer", "map3d");
    window.leftViewer = viewer;

    //labels 赋值
    labels = viewer.scene.primitives.add(new Cesium.LabelCollection());

    // 增加降雨等值面图层
    let contourGroupLayer = new DC.LayerGroup("contour");
    viewer.addLayerGroup(contourGroupLayer);
    let layer = new DC.VectorLayer("contour-layer");
    contourGroupLayer.addLayer(layer);
    // 列表查询，地图也查询怕重复提交，慢一秒钟
    window.EventBus.$on("rainMap/update", (hour) => {
      getRainList(viewer, hour);
    });
    setTimeout(() => {
      getLayerData(viewer);

      // getFloodData()
    }, 1000);
    // 增加icon 鼠标移入移出监听
    window.showMorePopInfo = (id) => {
      const div1 = document.getElementById(id + "pop1");
      const div2 = document.getElementById(id + "pop2");
      div1.style.display = "none";
      div2.style.display = "block";
      const parentElement = div1.parentNode;
      const grandparentElement = parentElement.parentNode;
      grandparentElement.style["z-index"] = 1;
    };
    window.hideMorePopInfo = (id) => {
      const div1 = document.getElementById(id + "pop1");
      const div2 = document.getElementById(id + "pop2");
      div2.style.display = "none";
      div1.style.display = "block";
      const parentElement = div1.parentNode;
      const grandparentElement = parentElement.parentNode;
      grandparentElement.style["z-index"] = "";
    };
    // 增加icon 鼠标移入移出监听
    window.showMorePopInfo2 = (id) => {
      // const div1 = document.getElementById(id + 'pop1')
      const div2 = document.getElementById(id + "pop2");
      // div1.style.display = "none"
      // div2.style.display = "block"
      const parentElement = div2.parentNode;
      const grandparentElement = parentElement.parentNode;
      grandparentElement.style["z-index"] = 1;
    };
    window.hideMorePopInfo2 = (id) => {
      // const div1 = document.getElementById(id + 'pop1')
      const div2 = document.getElementById(id + "pop2");
      // div2.style.display = "none"
      // div1.style.display = "block"
      const parentElement = div2.parentNode;
      const grandparentElement = parentElement.parentNode;
      grandparentElement.style["z-index"] = "";
    };
    window.showMorePopInfoRisk = (id) => {
      const div1 = document.getElementById(id + "pop");
      div1.style.display = "block";
      const parentElement = div1.parentNode;
      const grandparentElement = parentElement.parentNode;
      grandparentElement.style["z-index"] = 1;
    };
    window.hideMorePopInfoRisk = (id) => {
      const div1 = document.getElementById(id + "pop");
      div1.style.display = "none";
      const parentElement = div1.parentNode;
      const grandparentElement = parentElement.parentNode;
      grandparentElement.style["z-index"] = "";
    };

    window.showMorePopInfoPlace = (id) => {
      const div1 = document.getElementById(id + "pop");
      div1.style.display = "block";
      const parentElement = div1.parentNode;
      const grandparentElement = parentElement.parentNode;
      grandparentElement.style["z-index"] = 1;
    };
    window.hideMorePopInfoPlace = (id) => {
      const div1 = document.getElementById(id + "pop");
      div1.style.display = "none";
      const parentElement = div1.parentNode;
      const grandparentElement = parentElement.parentNode;
      grandparentElement.style["z-index"] = "";
    };
  });
});

// 获取村社列表
const getTownshipList = async (viewer) => {
  try {
    createGLayer("township"); // 确保图层已创建
    const res = await villageList({
      pageNum: 1,
      pageSize: 9999,
    });

    clearLayer("township", viewer);
    let layer = new DC.PrimitiveLayer("township-primitive");
    window.viewer.addLayer(layer);
    let hlayer = viewer.getLayer("township-layer-html");
    // console.log(res)
    if (res.data && res.data.records) {
      res.data.records.forEach((item) => {
        addTownshipData(item, layer, hlayer);
      });
    }
  } catch (error) {
    console.error("Failed to get township list:", error);
  }
};

// 获取企事业单位列表
const getEnterpriseList = async (viewer) => {
  try {
    createGLayer("enterprise"); // 确保图层已创建
    const res = await queryEnterpriseList({
      pageNum: 1,
      pageSize: 20,
    });

    clearLayer("enterprise", viewer);
    let layer = new DC.PrimitiveLayer("enterprise-primitive");
    window.viewer.addLayer(layer);
    let hlayer = viewer.getLayer("enterprise-layer-html");

    if (res && res.rows) {
      res.rows.forEach((item) => {
        addEnterpriseData(item, layer, hlayer);
      });
    }
  } catch (error) {
    console.error("Failed to get enterprise list:", error);
  }
};

// 获取居民住宅列表
const getResidentList = async (viewer) => {
  try {
    createGLayer("resident"); // 确保图层已创建
    const res = await residentList({
      pageNum: 1,
      pageSize: 20,
    });

    clearLayer("resident", viewer);
    let layer = new DC.PrimitiveLayer("resident-primitive");
    window.viewer.addLayer(layer);
    let hlayer = viewer.getLayer("resident-layer-html");

    if (res.data && res.data.records) {
      res.data.records.forEach((item) => {
        addResidentData(item, layer, hlayer);
      });
    }
  } catch (error) {
    console.error("Failed to get resident list:", error);
  }
};

// 获取风险隐患列表
const getRiskProblemList = async (viewer) => {
  try {
    createGLayer("risk-problem"); // 确保图层已创建
    const res = await queryRiskProblem({
      pageNum: 1,
      pageSize: 20,
    });

    clearLayer("risk-problem", viewer);
    let layer = new DC.PrimitiveLayer("risk-problem-primitive");
    window.viewer.addLayer(layer);
    let hlayer = viewer.getLayer("risk-problem-layer-html");

    if (res && res.rows) {
      res.rows.forEach((item) => {
        addRiskProblemData(item, layer, hlayer);
      });
    }
  } catch (error) {
    console.error("Failed to get risk problem list:", error);
  }
};

// 获取安置点列表
const getPlaceList = async (viewer) => {
  try {
    createGLayer("place"); // 确保图层已创建
    const res = await placeList({
      pageNum: 1,
      pageSize: 20,
    });

    clearLayer("place", viewer);
    let layer = new DC.PrimitiveLayer("place-primitive");
    window.viewer.addLayer(layer);
    let hlayer = viewer.getLayer("place-layer-html");

    if (res.data && res.data.records) {
      res.data.records.forEach((item) => {
        addPlaceData(item, layer, hlayer);
      });
    }
  } catch (error) {
    console.error("Failed to get place list:", error);
  }
};

// 获取危险区列表
const getDangerAreaList = async (viewer) => {
  try {
    createGLayer("dangerArea"); // 确保图层已创建
    const res = await dangerousAreaList({
      pageNum: 1,
      pageSize: 20,
    });

    clearLayer("dangerArea", viewer);
    let layer = viewer.getLayer("dangerArea-layer");
    let hlayer = viewer.getLayer("dangerArea-layer-html");
    if (res && res.data.records) {
      const resLength = res.data.records.length;
      const loopTimes = Math.min(resLength, 5);
      // 最多请求五个危险区
      for (let i = 0; i < loopTimes; i++) {
        let hazardouId = res.data.records[i].id;
        await dangerousAreaDetail(hazardouId).then((detailResult) => {
          if (detailResult && detailResult.data.geom) {
            let geom = JSON.parse(detailResult.data.geom);
            addDangerAreaData(geom, layer, hlayer, detailResult.data.name);
          }
        });
      }
    }
  } catch (error) {
    console.error("Failed to get danger area list:", error);
  }
};
// 获取村社转移安置点路线列表
const getRoadList = async (viewer) => {
  try {
    const res = await queryVillagePlacementList({
      pageNum: 1,
      pageSize: 20,
    });
    if (res && res.rows) {
      for (let i = 0; i < res.rows.length; i++) {
        //只循环5次，请求五个路线
        let id = res.rows[i].id;
        const detailResult = await queryVillagePlacementDetail(id);
        // console.log(detailResult)
        if (detailResult && detailResult.data.geom) {
          typeof detailResult.data.geom === "object"
            ? addRoad(detailResult.data.geom, null, null, id)
            : addRoad(JSON.parse(detailResult.data.geom), null, null, id);
        }
      }
    }
  } catch (error) {
    console.error("Failed to get danger area list:", error);
  }
};

// 获取水闸列表
const getWaterGateList = async (viewer) => {
  try {
    // 调用真实接口获取水闸数据
    const res = await getWaterGateDetail({ pageNum: 1, pageSize: 20 });
    const rows = res?.rows || [];
    let primitiveLayer = new DC.PrimitiveLayer("gate-primitive");
    window.viewer.addLayer(primitiveLayer);
    let htmlPrimitiveLayer = new DC.HtmlLayer("gate-primitive-html");
    window.viewer.addLayer(htmlPrimitiveLayer);
    rows.forEach((item) => {
      // 字段适配
      const data = {
        ...item,
        lgtd: item.longitude,
        lttd: item.latitude,
        name: item.gateName,
        dischargeFlow: item.waterLevelDataList[0]?.dischargeFlow, //下泄流量 (m³/s)
        upstreamLevel: item.waterLevelDataList[0]?.upstreamLevel, //闸上水位 (m)
        downstreamLevel: item.waterLevelDataList[0]?.downstreamLevel, //闸下水位 (m)
        recordTime: item.waterLevelDataList[0]?.recordTime, //记录时间
      };
      addWaterGateData(data, primitiveLayer, htmlPrimitiveLayer);
    });
  } catch (error) {
    console.error("Failed to get water gate list:", error);
  }
};

// 获取视频监控列表
const getVideoMonitoringList = async (viewer) => {
  try {
    // 使用mock数据
    const res = {
      code: 200,
      data: [
        {
          indexCode: "a8c341fb599c4384959eef10b9306556",
          name: "5号闸2号闸墩球机",
          latitude: 29.639,
          longitude: 91.1638,
        },
        {
          indexCode: "ae2d728e64bb4f5bad88e9d0c3358800",
          name: "5号闸4号闸墩球机",
          latitude: 29.6384,
          longitude: 91.163,
        },
        {
          indexCode: "04353192133c4642aba70451d2877196",
          name: "5号闸5号闸墩球机",
          latitude: 29.641,
          longitude: 91.165,
        },
        {
          indexCode: "121c005119dc4594953383e257a323dc",
          name: "5号闸6号闸墩球机",
          latitude: 29.64,
          longitude: 91.164,
        },
        {
          indexCode: "6c9009b8853d4c4e86d57eea8413f699",
          name: "5号闸7号闸墩球机",
          latitude: 29.642,
          longitude: 91.1651,
        },
      ],
    };

    if (res.code === 200) {
      clearLayer("video", viewer);
      const primitiveLayer = new DC.PrimitiveLayer(
        "video-monitoring-primitive"
      );
      window.viewer.addLayer(primitiveLayer);

      res.data.forEach((item) => {
        if (item.longitude && item.latitude) {
          addVideoMonitoringData(item, primitiveLayer);
        }
      });
    }
  } catch (error) {
    console.error("获取视频监控列表失败:", error);
  }
};

// 危险因素类型枚举函数
const getDangerTypeText = (type) => {
  const typeMap = {
    1: "临河滑坡",
    2: "泥石流",
    3: "低洼地",
    4: "急弯",
    5: "束窄",
    6: "流域",
    7: "多支齐汇",
    8: "桥梁",
  };
  return typeMap[type] || "--";
};
</script>

<style lang="scss" scoped>
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: #000c2f;
}

.map-main {
  width: 100%;
  height: 100%;
  display: inline-block;
  position: relative;
}

.width51 {
  position: fixed;
  left: 50%;
  width: 4px;
  background: #fff;
  top: 64px;
  bottom: 0;
  height: 100%;
  z-index: 10;
}

.duibiBtn {
  right: 50px;
  top: 30px;
  position: absolute;
}

.width50 {
  width: 50%;
  overflow: hidden;
  /* border-bottom: 275px solid #000c2f; */
}

.hideTwoMap {
  visibility: hidden;
}

/* 新增样式，调整三个组件的位置 */
/* :deep(.search-tool) {
  right: 20px !important;
  left: auto !important;
}

:deep(.area-select) {
  right: 20px !important;
  left: auto !important;
  top: 120px !important;
}

:deep(.group-panel) {
  right: 20px !important;
  left: auto !important;
  top: 220px !important;
} */

:deep(.el-select),
:deep(.el-input) {
  .el-input__suffix {
    .el-icon {
      svg {
        display: none;
      }

      &::before {
        content: " ";
        display: inline-block;
        width: 1rem;
        height: 1rem;
        background: url("@/assets/images/solidDown.png") no-repeat center center;
      }
    }
  }
}
</style>
<style>
.marker-rain-html {
  width: 82px;
  height: 26px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
  padding: 3px 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -40px;
}

.marker-rain-html2 {
  width: 168px;
  height: 135px;
  padding: 8px 8px;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -78px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}

.marker-river-html {
  width: 82px;
  height: 26px;
  padding: 4px 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 35px;
  left: -39px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}

.lenged-fix3 {
  position: fixed;
  display: flex;
  align-items: center;
  left: 50%;
  top: 50%;
  padding: 10px 30px 10px 12px;
  width: 760px;
  margin-left: -400px;
  margin-top: -40px;
  height: 63px;
  z-index: 20;
  background: rgba(0, 78, 196, 0.3);
  border: 1px solid #2ab3fc;
  border-radius: 4px;
  z-index: 999;
}

.marker-river-warn-html {
  width: 82px;
  height: 26px;
  padding: 2px 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 35px;
  left: -64px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}

.marker-river-warn-html2 {
  width: 168px;
  height: 135px;
  padding: 8px 8px;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 30px;
  left: -78px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}

.marker-reservoir-html {
  /*width: 142px;*/
  width: 82px;
  height: 26px;
  padding: 1px 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -39px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}

.marker-reservoir-warn-html {
  width: 82px;
  height: 26px;
  padding: 2px 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -39px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}

.marker-reservoir-warn-html2 {
  width: 168px;
  height: 135px;
  padding: 8px 8px;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -78px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}

.up-river {
  width: 142px;
  background: rgba(255, 122, 39, 0.74);
  border: 1px solid #ff7a27;
}

.down-river {
  width: 142px;
  background: rgba(255, 149, 19, 0.76);
  border: 1px solid #ff9513;
}

.ping-river {
  width: 142px;
  background: rgba(255, 213, 74, 0.68);
  border: 1px solid #ffd54a;
}

.over-limit {
  width: 142px;
  background: rgba(255, 213, 74, 0.68);
  border: 1px solid #ffeb98;
}

.up_marker {
  width: 25px;
  height: 25px;
  position: absolute;
  right: -14px;
  top: -10px;
  z-index: 10;
  background: #ff7e23;
  color: #fff;
  font-size: 12px;
  border-radius: 50%;
  line-height: 25px;
}

.down_marker {
  width: 25px;
  height: 25px;
  position: absolute;
  right: -14px;
  top: -10px;
  z-index: 10;
  background: rgb(29 190 39 / 99%);
  color: #fff;
  font-size: 12px;
  border-radius: 50%;
  line-height: 25px;
}

.ping_marker {
  width: 25px;
  height: 25px;
  position: absolute;
  right: -14px;
  top: -10px;
  z-index: 10;
  display: none;
  line-height: 25px;
}

.div-icon {
  background: none;
}

.hideMoreText {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.popHr {
  height: 2px;
  border: 0;
  margin: 2px;
  width: 100%;
  background: linear-gradient(to right, #0ec2fe 0%, rgb(255 255 255 / 0%) 100%);
}

.marker-township-html {
  width: 160px;
  height: 26px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
  padding: 3px 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -40px;
}

.marker-township-html2 {
  width: 168px;
  height: 150px;
  padding: 8px 8px;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -78px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}

.marker-enterprise-html {
  width: 160px;
  height: 26px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
  padding: 3px 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  position: absolute;
  bottom: 15px;
  left: -80px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.marker-enterprise-html2 {
  width: 200px;
  height: auto;
  padding: 12px;
  border-radius: 4px;
  color: #fff;
  position: absolute;
  bottom: 15px;
  left: -100px;
  background: rgba(0, 78, 196, 0.8);
  border: 1px solid #2ab3fc;
}

.enterprise-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.enterprise-content {
  padding: 0 4px;
}

.enterprise-item {
  margin: 6px 0;
  font-size: 13px;
  line-height: 1.4;
  display: flex;
}

.enterprise-label {
  flex-shrink: 0;
  color: #88ccff;
}

.enterprise-value {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 4px;
}

.marker-resident-html {
  width: 160px;
  height: 26px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
  padding: 3px 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -40px;
}

.marker-resident-html2 {
  width: 168px;
  height: auto;
  padding: 8px 8px;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -78px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}

.marker-risk-html {
  width: 160px;
  height: 26px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
  padding: 3px 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -40px;
}

.marker-risk-html2 {
  width: 168px;
  height: auto;
  padding: 8px 8px;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -78px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}

.marker-place-html {
  width: 160px;
  height: 26px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
  padding: 3px 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -40px;
}

.marker-place-html2 {
  width: 168px;
  height: auto;
  padding: 8px 8px;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -78px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}

.marker-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #fff;
  text-align: center;
  line-height: 1.4;
  word-wrap: break-word;
}

.marker-item {
  font-size: 14px;
}

.danger-area-container {
  position: relative;
  width: 160px;
  height: 26px;
  transform-origin: center;
  transform: translateX(-80px);
}

.danger-area-container:hover .danger-area-label {
  opacity: 0;
  visibility: hidden;
}

.danger-area-container:hover .danger-area-popup {
  opacity: 1;
  visibility: visible;
}

.danger-area-label {
  background: rgba(220, 38, 38, 0.85);
  border: 2px solid rgba(255, 255, 255, 0.8);
  padding: 3px 10px;
  text-align: center;
  border-radius: 6px;
  color: #fff;
  font-size: 15px;
  font-weight: 600;
  position: absolute;
  bottom: 0;
  left: 0;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
  white-space: nowrap;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.danger-area-popup {
  opacity: 0;
  visibility: hidden;
  width: 200px;
  height: auto;
  padding: 10px;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #0ec2fe;
  transition: all 0.3s ease;
  pointer-events: none;
}

.danger-area-popup .marker-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #fff;
  text-align: center;
  line-height: 1.4;
  word-wrap: break-word;
}

.danger-area-popup .marker-content {
  padding: 0 4px;
}

.danger-area-popup .marker-item {
  margin: 6px 0;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.danger-area-popup .marker-label {
  color: #88ccff;
  margin-right: 10px;
}

.danger-area-popup .marker-value {
  color: #fff;
  flex: 1;
  text-align: right;
}

/* 水闸主标签样式 */
.marker-watergate-html {
  width: 82px;
  height: 26px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
  padding: 3px 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -40px;
}

/* 水闸详情弹窗样式 */
.marker-watergate-html2 {
  width: 220px;
  min-height: 120px;
  padding: 16px 18px 14px 18px;
  border-radius: 8px;
  color: #fff;
  position: absolute;
  bottom: 15px;
  left: -110px;
  background: rgba(0, 78, 196, 0.7);
  border: 1px solid #2ab3fc;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.18);
}

.watergate-flow {
  font-size: 20px;
  font-weight: bold;
  color: #00eaff;
  margin-bottom: 10px;
  line-height: 1.1;
}

.watergate-name {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10px;
  line-height: 1.1;
}

.watergate-levels {
  display: flex;
  justify-content: space-between;
  width: 100%;
  font-size: 14px;
  margin-top: 10px;
  margin-bottom: 10px;
  line-height: 1.4;
}

.watergate-label {
  color: #88ccff;
  font-weight: 500;
  margin-right: 2px;
}

.watergate-value {
  color: #fff;
  font-weight: bold;
  margin-left: 2px;
}

.watergate-time {
  font-size: 13px;
  color: #b0c4de;
  margin-top: 2px;
  margin-bottom: 2px;
  line-height: 1.2;
}

.popHr {
  height: 1px;
  border: 0;
  margin: 6px 0;
  width: 100%;
  background: linear-gradient(to right, #0ec2fe 0%, rgb(255 255 255 / 0%) 100%);
}
</style>
<style lang="scss">
.div-icon {
  background: none;
  pointer-events: auto !important;
  cursor: pointer;
}
</style>
